// Right Panel Edit Texts
import React, { useEffect, useState } from "react";

interface Props {
    value: string;
    onSave: (newValue: string) => void;
    mode?: "text" | "tag";
    textarea?: boolean;
}

const EditableField: React.FC<Props> = ({ value, onSave, mode = "text", textarea = false }) => {
    const [editing, setEditing] = useState(false);
    const [inputValue, setInputValue] = useState(value);

    useEffect(() => {
        setInputValue(value);
    }, [value]);

    const handleSave = () => {
        setEditing(false);
        onSave(inputValue.trim());
    };

    if (editing) {
        if (textarea) {
            return (
                <textarea
                    className="border p-1 text-sm w-full resize-y rounded"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onBlur={handleSave}
                    autoFocus
                    rows={4}
                />
            );
        } else {
            return (
                <input
                    className="border p-1 text-sm w-full rounded"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onBlur={handleSave}
                    onKeyDown={(e) => {
                        if (e.key === "Enter") handleSave();
                    }}
                    autoFocus
                />
            );
        }
    }

    return (
        <div
            className={`cursor-pointer ${mode === "tag" ? "bg-gray-100 rounded px-2 py-0.5" : ""}`}
            onClick={() => setEditing(true)}
        >
            {value || <span className="text-gray-400 italic">Empty</span>}
        </div>
    );
};

export default EditableField;
