{"name": "reference-collector", "private": true, "version": "0.0.0", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "concurrently --kill-others-on-fail \"npm run dev:react\" \"npm run electron:wait\"", "dev:react": "vite", "electron:wait": "wait-on tcp:5173 && npm run dev:electron", "dev:electron": "npm run transpile:electron && cross-env NODE_ENV=development electron .", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "transpile:electron": "tsc --project src/electron/main/tsconfig.json", "dist:mac": "npm run transpile:electron && npm run build && electron-builder --mac --arm64", "dist:win": "npm run transpile:electron && npm run build && electron-builder --win --x64", "dist:linux": "npm run transpile:electron && npm run build && electron-builder --linux --x64"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/react": "^2.8.1", "@heroui/system": "^2.4.19", "@heroui/theme": "^2.4.17", "axios": "^1.11.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/axios": "^0.9.36", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "wait-on": "^8.0.3"}}