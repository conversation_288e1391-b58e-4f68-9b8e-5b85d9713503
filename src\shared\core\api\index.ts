import axios from "axios";

const API_URL = process.env.NODE_ENV === 'development' 
  ? "http://localhost:3000/api" 
  : process.env.NEXT_PUBLIC_API_URL || "https://your-api.com/api";

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// api.interceptors.request.use(
//   (config) => {
//     const token = getAuthToken();
//     if (token) {
//       config.headers.Authorization = `Bearer ${token}`;
//     }
//     return config;
//   },
//   (error) => Promise.reject(error)
// );

// api.interceptors.response.use(
//   (response) => response,
//   (error) => {
//     if (error.response?.status === 401) {
//       handleUnauthorized();
//     }
    
//     handleApiError(error);
//     return Promise.reject(error);
//   }
// );

// function getAuthToken(): string | null {
//   if (typeof Office !== 'undefined') {
//     return Office.context.roamingSettings?.get('auth_token') || null;
//   }
  
//   if (typeof localStorage !== 'undefined') {
//     return localStorage.getItem('access_token');
//   }
  
//   if (typeof chrome !== 'undefined' && chrome.storage) {
//   }
  
//   return null;
// }

// function handleUnauthorized() {
//   // Platform'a göre farklı logout logic
// }

// function handleApiError(error: any) {
//   console.error('API Error:', error);
//   // Platform'a göre notification göster
// }