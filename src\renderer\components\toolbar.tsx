import { useState, useEffect } from "react";
import { But<PERSON> } from "@heroui/react";
import { Minus, Square, X, Monitor } from "lucide-react";

export default function Toolbar() {
    const [isMaximized, setIsMaximized] = useState(false);

    useEffect(() => {
        window.electron?.onMaximizeChanged?.((maximized: boolean) => {
            setIsMaximized(maximized);
        });
    }, []);

    return (
        <div className="w-full h-7 flex items-center justify-between px-3 border-b bg-white text-sm select-none drag">
            <div className="flex items-center gap-2">
                <Monitor className="w-4 h-4 text-indigo-600 no-drag" />
                <span className="font-medium text-gray-800 no-drag">Reference Collector</span>
            </div>

            <div className="flex items-center gap-1 no-drag ml-auto">
                {/* Toolbar: Minimize Button */}
                <Button
                    size="sm"
                    variant="ghost"
                    className="!h-5 !w-5 !min-w-4 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-150"
                    onPress={() => window.electron?.minimize?.()}
                    aria-label="Minimize"
                >
                    <Minus className="w-2.5 h-2.5" />
                </Button>

                {/* Toolbar: Maximize Button */}
                <Button
                    size="sm"
                    variant="ghost"
                    className="!h-5 !w-5 !min-w-4 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-150"
                    onPress={() => window.electron?.maximize?.()}
                    aria-label={isMaximized ? "Restore" : "Maximize"}
                >
                    {isMaximized ? (
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth={2.5}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="w-2.5 h-2.5"
                        >
                            <path d="M3 12V3h9" />
                            <rect x="9" y="9" width="12" height="12" />
                        </svg>
                    ) : (
                        <Square className="w-2.5 h-2.5" />
                    )}
                </Button>

                {/* Toolbar: Exit Button */}
                <Button
                    size="sm"
                    variant="ghost"
                    className="!h-5 !w-5 !min-w-4 p-0 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-150"
                    onPress={() => window.electron?.close?.()}
                    aria-label="Close"
                >
                    <X className="w-2.5 h-2.5" />
                </Button>
            </div>
        </div>
    );
}