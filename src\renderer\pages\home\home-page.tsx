import React from 'react';
import '../../style/index.css';
import {Hero<PERSON>Provider} from '@heroui/system'

// Panels
import MiddlePanel from './layouts/middle-panel/middle-panel.tsx';
import Navbar from './layouts/navbar/navbar.tsx';
import {RightToolbar} from "./layouts/right-panel/right-toolbar.tsx";

// AppProvider: Provides global state management for the app.
// Enables communication and data sharing between panels,
import { AppProvider } from "../../components/context/app-provider.tsx";

// Resizable Panels
import ResizableRightPanel from "../../components/resize-layouts/resizable-right-panel.tsx";
import ResizableLeftPanel from "../../components/resize-layouts/resizable-left-panel.tsx";

// Toolbar Panel
import Toolbar from "../../components/toolbar.tsx"

const HomePage: React.FC = () => {
    return (
        <AppProvider>
            <div className="select-none min-h-screen bg-gray-50 text-sm text-gray-800 font-sans">
                <Toolbar />
                <nav className="h-[45px] flex-shrink-0 m-0 p-0">
                    <Navbar />
                </nav>

                <div className="flex h-[calc(100vh-88px)]">
                    <ResizableLeftPanel />
                    <MiddlePanel />
                    <ResizableRightPanel/>
                    <RightToolbar/>
                </div>
            </div>
        </AppProvider>
    )
}

export function App() {
    return (
        <HeroUIProvider>
            <HomePage />
        </HeroUIProvider>
    );
}