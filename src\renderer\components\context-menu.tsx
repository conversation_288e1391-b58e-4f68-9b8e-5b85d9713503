// Context Menu : Right Click
import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

export interface ContextMenuItem {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
    disabled?: boolean;
}

export interface ContextMenuSection {
    items: ContextMenuItem[];
}

interface ContextMenuProps {
    x: number;
    y: number;
    sections: ContextMenuSection[];
    onClose: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, sections, onClose }) => {
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
                onClose();
            }
        };

        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscape);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleEscape);
        };
    }, [onClose]);

    useEffect(() => {
        if (menuRef.current) {
            const menu = menuRef.current;
            const rect = menu.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            let adjustedX = x;
            let adjustedY = y;

            if (x + rect.width > viewportWidth) {
                adjustedX = viewportWidth - rect.width - 10;
            }

            if (y + rect.height > viewportHeight) {
                adjustedY = viewportHeight - rect.height - 10;
            }

            menu.style.left = `${Math.max(0, adjustedX)}px`;
            menu.style.top = `${Math.max(0, adjustedY)}px`;
        }
    }, [x, y]);

    const menuContent = (
        <div
            ref={menuRef}
            className="fixed bg-white border border-gray-200 rounded-lg shadow-xl py-1 min-w-[180px] z-[9999] backdrop-blur-sm"
            style={{
                left: x,
                top: y,
            }}
        >
            {sections.map((section, sectionIndex) => (
                <div key={sectionIndex}>
                    {section.items.map((item, itemIndex) => (
                        <button
                            key={itemIndex}
                            className={`w-full text-left px-3 py-2 text-xs flex items-center gap-3 transition-all duration-150 ${
                                item.disabled
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : item.label.toLowerCase().includes('delete') || item.label.toLowerCase().includes('trash')
                                        ? 'text-red-600 hover:bg-red-50 active:bg-red-100 hover:text-red-700'
                                        : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'
                            }`}
                            onClick={() => {
                                if (!item.disabled) {
                                    item.onClick();
                                    onClose();
                                }
                            }}
                            disabled={item.disabled}
                        >
                            {item.icon && (
                                <span className={`flex-shrink-0 w-4 h-4 ${
                                    item.label.toLowerCase().includes('delete')
                                        ? 'text-red-500'
                                        : 'text-gray-500'
                                }`}>
                                    {item.icon}
                                </span>
                            )}
                            <span className="font-medium">{item.label}</span>
                        </button>
                    ))}
                    {sectionIndex < sections.length - 1 && (
                        <div className="h-px bg-gray-200 mx-2 my-1" />
                    )}
                </div>
            ))}
        </div>
    );

    return createPortal(menuContent, document.body);
};

export default ContextMenu;