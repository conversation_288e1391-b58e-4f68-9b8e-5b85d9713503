import React, {type ReactNode} from "react";
import {
    BookOpenIcon,
    UserIcon,
    TagIcon,
    CalendarIcon,
    DocumentIcon,
    PaperClipIcon,
    PencilSquareIcon,
    BuildingLibraryIcon,
    HashtagIcon,
    LinkIcon,
    DocumentDuplicateIcon,
    GlobeAltIcon,
    DocumentTextIcon, ClockIcon, InformationCircleIcon, FolderIcon, PlusIcon
} from "@heroicons/react/24/outline";

// Editable Field for Right Panel
import EditableField from "../../../../components/editable-field.tsx";

// AppProvider: Provides global state management for the app.
// Enables communication and data sharing between panels,
import { useAppContext } from "../../../../components/context/use-app-context.ts";
import {Button, Tooltip} from "@heroui/react";

// Categories ( Info, Abstract etc... )
const Card = ({
                  title,
                  icon: Icon,
                  color, children
}: {
                  title: ReactNode;
                  icon: React.ElementType;
                  color: string;
                  children: React.ReactNode }) => (
    <div className="bg-white rounded-md shadow-sm p-3 mb-4 border border-gray-200 w-full max-w-full">
        <div className={`flex items-center gap-1.5 mb-2 text-[15px] font-semibold ${color}`}>
            <Icon className={`h-5 w-5 ${color}`} />
            {title}
        </div>

        <div className="space-y-2 text-[14px] text-gray-800">{children}</div>
    </div>
);

// Categories' Contents
const InfoField = ({ icon: Icon, label, content, editable = false }: {
    icon: React.ElementType;
    label: string;
    content: React.ReactNode;
    editable?: boolean;
}) => (
    <div className="flex flex-wrap items-center gap-1.5">
        <Icon className="h-4 w-4 text-gray-900 flex-shrink-0" />
        <span className="font-medium text-gray-600">{label}:</span>
        <div
            className={`flex-1 min-w-0 break-words ${
                editable ? "cursor-pointer bg-gray-100 rounded px-1" : "text-gray-500 italic"
            }`}
        >
            {content}
        </div>
    </div>
);

export const RightPanel: React.FC = () => {
    const { selectedFile, setSelectedFile } = useAppContext();

    if (!selectedFile) {
        return (
            <div className="flex flex-col w-full h-full bg-white px-4 py-3 overflow-y-auto text-gray-800 font-sans border-l text-sm">
                <div className="text-sm font-semibold flex items-center justify-center h-8 leading-5 pt-1">Details</div>
                <div className="text-center text-gray-500 mt-10">
                    <DocumentIcon className="h-10 w-10 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">Select a file to view details</p>
                </div>
            </div>
        );
    }

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 90 }, (_, i) => (currentYear - i).toString());

    return (
        <div className="flex flex-col w-full h-full bg-gray-50 px-5 py-4 overflow-y-auto">

            {/* Info Card */}
            <Card title="Info" icon={InformationCircleIcon} color="text-blue-900">
                <div className="space-y-5">
                    <InfoField
                        icon={BookOpenIcon}
                        label="Title"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.name.replace(/\.[^/.]+$/, "")}
                                onSave={(newVal: string) => {
                                    const extension = selectedFile.name.match(/\.[^/.]+$/)?.[0] || "";
                                    setSelectedFile({ ...selectedFile, name: newVal + extension });
                                }}
                            />
                        }
                    />
                    <InfoField
                        icon={UserIcon}
                        label="Creator"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.creator || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, creator: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={TagIcon}
                        label="Type"
                        editable={true}
                        content={
                            <EditableField
                                value={(selectedFile.type || "").toUpperCase()}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, type: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={DocumentIcon}
                        label="Item Type"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.itemType || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, itemType: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={CalendarIcon}
                        label="Year"
                        content={
                            <select
                                className="border p-1 rounded w-full text-sm"
                                value={selectedFile.year || ""}
                                onChange={(e) => setSelectedFile({ ...selectedFile, year: e.target.value })}
                            >
                                <option value="">Select year</option>
                                {years.map((year) => (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                ))}
                            </select>
                        }
                    />
                    <InfoField
                        icon={BuildingLibraryIcon}
                        label="Series"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.series || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, series: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={DocumentDuplicateIcon}
                        label="Edition"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.edition || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, edition: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={GlobeAltIcon}
                        label="Language"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.language || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, language: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={HashtagIcon}
                        label="ISBN"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.isbn || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, isbn: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={LinkIcon}
                        label="URL"
                        editable={true}
                        content={
                            <EditableField
                                value={selectedFile.url || ""}
                                onSave={(newVal: string) => setSelectedFile({ ...selectedFile, url: newVal })}
                            />
                        }
                    />
                    <InfoField
                        icon={DocumentTextIcon}
                        label="Pages"
                        editable={true}
                        content={
                            <EditableField
                                value={Array.isArray(selectedFile.pages) ? selectedFile.pages.join(", ") : ""}
                                onSave={(newVal: string) =>
                                    setSelectedFile({
                                        ...selectedFile,
                                        pages: newVal
                                            .split(",")
                                            .map((p) => p.trim())
                                            .filter((p) => p !== ""),
                                    })
                                }
                            />
                        }
                    />
                    <InfoField
                        icon={ClockIcon}
                        label="Date Added"
                        editable={false}
                        content={<div>{selectedFile.dateAdded}</div>}
                    />
                    <InfoField
                        icon={PencilSquareIcon}
                        label="Modified"
                        editable={false}
                        content={<div>{selectedFile.modified}</div>}
                    />
                </div>
            </Card>

            {/* Abstract Card */}
            <Card title="Abstract" icon={DocumentTextIcon} color="text-green-900">
                <EditableField
                    value={selectedFile.abstract || ""}
                    onSave={(newVal: string) => setSelectedFile({ ...selectedFile, abstract: newVal })}
                    textarea
                />
            </Card>

            {/* Attachment Card */}
            <Card
                icon={PaperClipIcon}
                color="text-purple-900"
                title={
                    <div className="flex items-center w-full">
                        <span className="flex-grow">Attachments</span>
                        <Tooltip content="New Attachment">
                            <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                                <PlusIcon className="h-3 w-3" />
                            </Button>
                        </Tooltip>
                    </div>
                }>
                <div>Attachments details here...</div>
            </Card>

            {/* Notes Card */}
            <Card
                icon={BookOpenIcon}
                color="text-yellow-900"
                title={
                    <div className="flex items-center w-full">
                        <span className="flex-grow">Notes</span>
                        <Tooltip content="New Notes">
                            <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                                <PlusIcon className="h-3 w-3" />
                            </Button>
                        </Tooltip>
                    </div>
                }>
                <div>Notes details here...</div>
            </Card>

            {/* Libraries and Collections Card */}
            <Card
                icon={FolderIcon}
                color="text-indigo-900"
                title={
                    <div className="flex items-center w-full">
                        <span className="flex-grow">Libraries and Collections</span>
                        <Tooltip content="New Collection">
                            <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                                <PlusIcon className="h-3 w-3" />
                            </Button>
                        </Tooltip>
                    </div>
                }>
                <div>Libraries and Collections details here...</div>
            </Card>

            {/* Tags Card */}
            <Card
                icon={TagIcon}
                color="text-pink-700"
                title={
                    <div className="flex items-center w-full">
                        <span className="flex-grow">Tags</span>
                        <Tooltip content="New Tag">
                            <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                                <PlusIcon className="h-3 w-3" />
                            </Button>
                        </Tooltip>
                    </div>
                }>
                <div className="flex flex-wrap gap-1">
                    {selectedFile.tags?.length > 0 ? (
                        selectedFile.tags.map((tag, idx) => (
                            <EditableField
                                key={idx}
                                value={tag}
                                mode="tag"
                                onSave={(newVal: string) => {
                                    const updatedTags = [...selectedFile.tags];
                                    updatedTags[idx] = newVal.trim();
                                    setSelectedFile({ ...selectedFile, tags: updatedTags });
                                }}
                            />
                        ))
                    ) : (
                        <div>No tags available</div>
                    )}
                </div>
            </Card>

            {/* Related Card */}
            <Card
                icon={LinkIcon}
                color="text-teal-900"
                title={
                <div className="flex items-center w-full">
                    <span className="flex-grow">Related</span>
                    <Tooltip content="New Related">
                        <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                            <PlusIcon className="h-3 w-3" />
                        </Button>
                    </Tooltip>
                </div>
            }>
                <div>Related items here...</div>
            </Card>

        </div>
    );
};
