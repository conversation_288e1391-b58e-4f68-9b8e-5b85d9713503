/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Citation } from "../types";
import type { ApiResponse } from "../types/response";
import { api } from "./api";

export abstract class BasePlugin {

    abstract insertCitation(citation: Citation): Promise<void>;
    abstract getCurrentDocument(): Promise<any>;
    abstract showNotification(message: string): void;

    async getCitations(): Promise<ApiResponse<Citation[]>> {
        try {
            const response = await api.get('/citations');
            return response.data as ApiResponse<Citation[]>;
        } catch (error:any) {
            console.log(error);
            return error.response.data as ApiResponse<Citation[]>;
        }
    }

    async saveCitation(citation: Citation): Promise<ApiResponse<Citation> | null> {
        try {
            const response = await api.post('/citations', citation);
            return response.data as ApiResponse<Citation>;
        } catch (error) {
            console.log(error);
            return null;
        }
    }
}