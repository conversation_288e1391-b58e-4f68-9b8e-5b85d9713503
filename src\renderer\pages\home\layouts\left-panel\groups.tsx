import React, { useState } from 'react';

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@heroui/react";

import {CheckIcon, PlusIcon} from "@heroicons/react/24/outline";
import {ChevronLeftIcon, XMarkIcon} from "@heroicons/react/24/solid";
import toast from "react-hot-toast";

import {userGroups} from "../../../../data/user-group.ts";

const Groups: React.FC = () => {
    // Group Work Modal States
    const [isGroupModalOpen, setIsGroupModalOpen] = useState(false);
    const [groupName, setGroupName] = useState('');
    const [groupType, setGroupType] = useState<'public-open' | 'public-closed' | 'private'>('public-open');
    const [currentStep, setCurrentStep] = useState(1);
    // Handle : Next
    const handleGroupNext = () => {
        if (currentStep === 1 && groupName.trim()) {
            setCurrentStep(2);
        }
        else if (currentStep === 2) {
            toast.success(`Group "${groupName}" created successfully!"`)
            handleGroupClose();
        }
    };
    // Handle : Back
    const handleGroupBack = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };
    // Handle : Close
    const handleGroupClose = () => {
        setCurrentStep(1);
        setGroupName('');
        setGroupType('public-open');
        setIsGroupModalOpen(false);
    }
    const canProceed = currentStep === 1 ? groupName.trim() : true;

    const groupTypeOptions = [
        {
            value: 'public-open' as const,
            title: 'Public, Open Membership',
            description: 'Anyone can view your group online and join the group instantly.',
            icon: '🌍'
        },
        {
            value: 'public-closed' as const,
            title: 'Public, Closed Membership',
            description: 'Anyone can view your group online, but members must apply or be invited.',
            icon: '🔒'
        },
        {
            value: 'private' as const,
            title: 'Private Membership',
            description: 'Only members can view your group online and must be invited to join.',
            icon: '🔐'
        }
    ];

    // Settings
    const [isSettingsOpen, setIsSettingsOpen] = useState(false);

    // Preview Image
    const [previewImage, setPreviewImage] = useState<string | null>(null);

    // Settings/Library/Members Buttons
    const [activeMembersGroupId, setActiveMembersGroupId] = useState<string | null>(null);
    const [activeLibraryGroupId, setActiveLibraryGroupId] = useState<string | null>(null);

    // Hide/Open Group List
    const [showGroupList, setShowGroupList] = useState(true);

    return(
        <>
            <div className="flex items-center justify-between px-3 py-2 border-b">
                <Button
                    size="sm"
                    variant="light"
                    className="text-sm font-semibold flex items-center justify-center h-8 leading-5 pt-1"
                    onPress={() => setShowGroupList(prev => !prev)}
                >
                    Group Work
                </Button>

                <Tooltip content="New Group Work">
                    <Button
                        isIconOnly
                        variant="ghost"
                        className="p-1 h-5 w-5 min-w-0"
                        onPress={() => setIsGroupModalOpen(true)}
                    >
                        <PlusIcon className="h-3 w-3" />
                    </Button>
                </Tooltip>
            </div>

            {/* List Existing Groups */}
            {showGroupList && (
                <div className="px-3 py-2 space-y-3">
                    {userGroups.map((group) => (
                        <div
                            key={group.id}
                            className="border border-gray-200 rounded-md p-3 text-xs bg-white shadow-sm"
                        >
                            <div className="flex items-center gap-2 text-sm font-semibold text-gray-800">
                                <span className="text-lg">{group.icon}</span>
                                <span className="truncate">{group.name}</span>
                            </div>

                            <Divider className="my-2"/>

                            {/* Group Settings */}
                            <div className="flex items-center gap-2 mb-2 text-[11px] text-gray-500">
                                <Button
                                    variant="light"
                                    size="sm"
                                    className="underline text-gray-500 text-[11px]"
                                    onPress={() => setIsSettingsOpen(true)}
                                >
                                    Settings
                                </Button>

                                {isSettingsOpen && (
                                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                                        <div className="bg-white rounded-xl shadow-xl w-[700px] max-h-[90vh] overflow-y-auto p-6">

                                            {/* Header */}
                                            <div className="flex items-center justify-between mb-4">
                                                <h2 className="text-lg font-semibold text-gray-900">Group Settings</h2>
                                                <Button
                                                    isIconOnly
                                                    variant="ghost"
                                                    size="sm"
                                                    className="p-1 h-8 w-8 min-w-0"
                                                    onPress={() => setIsSettingsOpen(false)}
                                                >
                                                    <XMarkIcon className="h-4 w-4" />
                                                </Button>
                                            </div>

                                            <Divider className="mb-6" />

                                            {/* Main Content */}
                                            <div className="flex gap-6">
                                                {/* Left Form Fields */}
                                                <div className="flex-1 space-y-6 text-sm text-gray-700">
                                                    {/* Group Name */}
                                                    <div>
                                                        <label className="block font-medium mb-1">Group Name</label>
                                                        <input
                                                            type="text"
                                                            value={groupName}
                                                            onChange={(e) => setGroupName(e.target.value)}
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:outline-none"
                                                        />
                                                    </div>

                                                    {/* Description */}
                                                    <div>
                                                        <label className="block font-medium mb-1">
                                                            Description <span className="text-gray-400">(What is this group about?)</span>
                                                        </label>
                                                        <textarea
                                                            placeholder="Describe your group..."
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:outline-none"
                                                        />
                                                    </div>

                                                    {/* Disciplines */}
                                                    <div>
                                                        <label className="block font-medium mb-1">Disciplines</label>
                                                        <div className="space-y-1">
                                                            {['Philosophy', 'Computer Science', 'Sociology', 'Economics'].map((d) => (
                                                                <label key={d} className="flex items-center gap-2 text-gray-700 text-sm">
                                                                    <input type="checkbox" className="text-blue-600" />
                                                                    {d}
                                                                </label>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Right Side Panel */}
                                                <div className="w-[200px] flex flex-col items-center gap-4">

                                                    {/* Profile Image Upload */}
                                                    <div className="flex flex-col items-center w-full">
                                                        <label className="block text-sm font-medium mb-2 text-center">Profile Image</label>
                                                        {previewImage && (
                                                            <img
                                                                src={previewImage}
                                                                alt="Preview"
                                                                className="w-24 h-24 object-cover rounded-full border border-gray-300 mb-2"
                                                            />
                                                        )}

                                                        <input
                                                            type="file"
                                                            accept="image/*"
                                                            onChange={(e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file) {
                                                                    const reader = new FileReader();
                                                                    reader.onloadend = () => setPreviewImage(reader.result as string);
                                                                    reader.readAsDataURL(file);
                                                                }
                                                            }}
                                                            className="block w-full text-xs text-gray-500 file:mr-2 file:py-1 file:px-file:rounded file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                                        />
                                                    </div>

                                                    {/* Owner Options */}
                                                    <div className="mt-6 w-full">
                                                        <h4 className="text-sm font-semibold text-gray-700 mb-2">Owner Options</h4>
                                                        <Button
                                                            variant="solid"
                                                            size="sm"
                                                            className="bg-red-600 text-white w-full"
                                                        >
                                                            Delete Group
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>

                                            <Divider className="my-6" />

                                            {/* Footer Buttons */}
                                            <div className="flex justify-end gap-2">
                                                <Button variant="light" size="sm" onPress={() => setIsSettingsOpen(false)}>
                                                    Cancel
                                                </Button>
                                                <Button variant="solid" size="sm" className="bg-blue-600 text-white">
                                                    Save Changes
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Members Settings */}
                                <Button
                                    variant="light"
                                    size="sm"
                                    className="underline text-gray-500 text-[11px]"
                                    onPress={() => setActiveMembersGroupId(String(group.id))}
                                >
                                    Members
                                </Button>
                                {activeMembersGroupId === String(group.id) && (
                                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                                        <div className="bg-white rounded-xl p-6 w-[600px] shadow-xl max-h-[90vh] overflow-y-auto">
                                            <div className="flex justify-between items-center mb-4">
                                                <h2 className="text-lg font-semibold text-gray-900">Group Members</h2>
                                                <Button
                                                    isIconOnly
                                                    variant="ghost"
                                                    size="sm"
                                                    onPress={() => setActiveMembersGroupId(null)}
                                                >
                                                    <XMarkIcon className="w-4 h-4" />
                                                </Button>
                                            </div>
                                            <Divider className="mb-4" />
                                            <table className="w-full text-sm text-left mb-6">
                                                <thead className="text-gray-500 text-xs">
                                                <tr>
                                                    <th className="py-2">Username</th>
                                                    <th>Full Name</th>
                                                    <th>Member Since</th>
                                                    <th>Role</th>
                                                </tr>
                                                </thead>
                                                <tbody className="text-gray-700 text-sm">
                                                <tr>
                                                    <td className="py-2">erencagsak</td>
                                                    <td>erencagsak</td>
                                                    <td>2025-06-24 08:23:35</td>
                                                    <td>Owner</td>
                                                </tr>
                                                </tbody>
                                            </table>

                                            <Button
                                                size="sm"
                                                className="bg-blue-600 text-white mb-4"
                                                onPress={() => toast.success("Roles updated")}
                                            >
                                                Update Roles
                                            </Button>

                                            <Divider className="my-4" />

                                            <h3 className="text-sm font-semibold mb-2">Member Invitations</h3>
                                            <p className="text-xs text-gray-500 mb-3">No pending invitations.</p>
                                            <Button
                                                size="sm"
                                                variant="bordered"
                                                onPress={() => toast.success("Invitation sent")}
                                            >
                                                Send More Invitations
                                            </Button>
                                        </div>
                                    </div>
                                )}

                                {/* Library Settings */}
                                <Button
                                    variant="light"
                                    size="sm"
                                    className="underline text-gray-500 text-[11px]"
                                    onPress={() => setActiveLibraryGroupId(String(group.id))}
                                >
                                    Library
                                </Button>
                                {activeLibraryGroupId === String(group.id) && (
                                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                                        <div className="bg-white rounded-xl p-6 w-[600px] shadow-xl max-h-[90vh] overflow-y-auto">
                                            <div className="flex justify-between items-center mb-4">
                                                <h2 className="text-lg font-semibold text-gray-900">Group Library Settings</h2>
                                                <Button
                                                    isIconOnly
                                                    variant="ghost"
                                                    size="sm"
                                                    onPress={() => setActiveLibraryGroupId(null)}
                                                >
                                                    <XMarkIcon className="w-4 h-4" />
                                                </Button>
                                            </div>

                                            <Divider className="mb-4" />

                                            {/* Group Type */}
                                            <div className="mb-5">
                                                <label className="block text-sm font-medium mb-1">Group Type</label>
                                                <select className="w-full border rounded-md p-2 text-sm">
                                                    <option>Private</option>
                                                    <option>Public Closed</option>
                                                    <option>Public Open</option>
                                                </select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Controls who can see and join your group
                                                </p>
                                            </div>

                                            {/* Library Reading */}
                                            <div className="mb-5">
                                                <label className="block text-sm font-medium mb-1">Library Reading</label>
                                                <select className="w-full border rounded-md p-2 text-sm">
                                                    <option>Anyone on the internet</option>
                                                    <option>Any group member</option>
                                                </select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Who can see items in this group's library?
                                                </p>
                                            </div>

                                            {/* Library Editing */}
                                            <div className="mb-5">
                                                <label className="block text-sm font-medium mb-1">Library Editing</label>
                                                <select className="w-full border rounded-md p-2 text-sm">
                                                    <option>Any group members</option>
                                                    <option>Only group admins</option>
                                                </select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Who can add, edit, and remove items from this group's library?
                                                </p>
                                            </div>

                                            {/* File Editing */}
                                            <div className="mb-5">
                                                <label className="block text-sm font-medium mb-1">File Editing</label>
                                                <select className="w-full border rounded-md p-2 text-sm">
                                                    <option>Any group members</option>
                                                    <option>Only group admins</option>
                                                    <option>No group file storage</option>
                                                </select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Who can work with files stored in the group? Public Open groups cannot have file storage enabled.
                                                </p>
                                            </div>

                                            <Button
                                                className="bg-blue-600 text-white mt-4"
                                                size="sm"
                                                onPress={() => toast.success("Library settings saved")}
                                            >
                                                Save Settings
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="text-gray-600 mb-1">
                                <span className="font-medium">Members:</span> {group.members.length} {group.members.length === 1 ? "person" : "people"}
                            </div>
                            <div className="text-gray-600 mb-1">
                                <span className="font-medium">Group Type:</span>{" "}
                                {group.groupType === "public-open"
                                    ? "Public (Open)"
                                    : group.groupType === "public-closed"
                                        ? "Public (Closed)"
                                        : "Private"}
                            </div>
                            <div className="text-gray-600">
                                <span className="font-medium">Group Library:</span> {group.libraryAccess}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Create New Group */}
            {isGroupModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-xl p-6 w-[480px] shadow-xl max-h-[90vh] overflow-y-auto">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                                {currentStep > 1 && (
                                    <Button
                                        isIconOnly
                                        variant="ghost"
                                        size="sm"
                                        className="p-1 h-8 w-8 min-w-0"
                                        onPress={handleGroupBack}
                                    >
                                        <ChevronLeftIcon className="h-4 w-4" />
                                    </Button>
                                )}
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Create New Group ({currentStep}/2)
                                </h2>
                            </div>
                            <Button
                                isIconOnly
                                variant="ghost"
                                size="sm"
                                className="p-1 h-8 w-8 min-w-0"
                                onPress={handleGroupClose}
                            >
                                <XMarkIcon className="h-4 w-4" />
                            </Button>
                        </div>

                        <Divider className="mb-6" />

                        {/* Step 1: Group Name */}
                        {currentStep === 1 && (
                            <div className="space-y-6">
                                <div>
                                    <label htmlFor="groupName" className="block text-sm font-semibold text-gray-900 mb-2">
                                        Group Name
                                    </label>
                                    <input
                                        type="text"
                                        id="groupName"
                                        value={groupName}
                                        onChange={(e) => setGroupName(e.target.value)}
                                        placeholder="Choose a name"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                        autoFocus
                                    />
                                    {groupName.trim() && (
                                        <p className="text-xs text-gray-500 mt-1">
                                            Group name: "{groupName}"
                                        </p>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Step 2: Group Type */}
                        {currentStep === 2 && (
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-sm font-semibold text-gray-900 mb-4">Group Type</h3>

                                    <div className="space-y-3">
                                        {groupTypeOptions.map((option) => {
                                            const isSelected = groupType === option.value;

                                            return (
                                                <label
                                                    key={option.value}
                                                    className={`block p-4 border rounded-lg cursor-pointer transition-all ${
                                                        isSelected
                                                            ? 'border-blue-500 bg-blue-50 shadow-sm'
                                                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    <div className="flex items-start">
                                                        <div className="flex items-center h-5 mt-0.5">
                                                            <input
                                                                type="radio"
                                                                name="groupType"
                                                                value={option.value}
                                                                checked={isSelected}
                                                                onChange={(e) => setGroupType(e.target.value as never)}
                                                                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                            />
                                                        </div>
                                                        <div className="ml-3 flex-1">
                                                            <div className="flex items-center mb-1">
                                                                <span className="mr-2 text-lg">{option.icon}</span>
                                                                <h4 className="text-sm font-semibold text-gray-900">
                                                                    {option.title}
                                                                </h4>
                                                            </div>
                                                            <p className="text-sm text-gray-600">
                                                                {option.description}
                                                            </p>
                                                        </div>
                                                        {isSelected && (
                                                            <CheckIcon className="w-5 h-5 text-blue-600 ml-2 mt-0.5" />
                                                        )}
                                                    </div>
                                                </label>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Selected group info */}
                                <div className="bg-gray-50 p-3 rounded-lg">
                                    <p className="text-xs text-gray-600">
                                        <span className="font-medium">Selected:</span> {groupTypeOptions.find(opt => opt.value === groupType)?.title}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">
                                        Group name: "{groupName}"
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Footer */}
                        <Divider className="my-6" />
                        <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-500">
                                Step {currentStep} of 2
                            </div>

                            <div className="flex items-center gap-2">
                                <Button
                                    variant="light"
                                    size="sm"
                                    onPress={handleGroupClose}
                                    className="border border-gray-300"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="solid"
                                    size="sm"
                                    onPress={handleGroupNext}
                                    disabled={!canProceed}
                                    className={`${!canProceed ? 'opacity-50 cursor-not-allowed' : ''} bg-blue-600 text-white`}
                                >
                                    {currentStep === 1 ? 'Next' : 'Create Group'}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default Groups;