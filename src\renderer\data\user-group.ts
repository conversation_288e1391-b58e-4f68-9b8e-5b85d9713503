export interface UserGroup {
    id: number;
    name: string;
    icon: string;
    members: string[];
    groupType: "public-open" | "public-closed" | "private";
    libraryAccess: string;
}

export const userGroups: UserGroup[] = [
    {
        id: 1,
        name: "Open Science Team",
        icon: "🌍",
        members: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
        groupType: "public-open",
        libraryAccess: "Anyone can view, members can edit"
    },
    {
        id: 2,
        name: "Private Research Group",
        icon: "👥",
        members: ["<PERSON>ren"],
        groupType: "private",
        libraryAccess: "Only members can view, only admins can edit"
    },
    {
        id: 3,
        name: "History Researchers",
        icon: "🔬",
        members: ["<PERSON><PERSON>", "<PERSON><PERSON>"],
        groupType: "public-closed",
        libraryAccess: "Only members can view, only admins can edit"
    }
];
