import React, {useState} from 'react';
import {<PERSON><PERSON>, Divider} from "@heroui/react";
import {useAppContext} from '../../../../components/context/use-app-context.ts';
import type {FileItem} from "../../../../data/user-folder.ts";
import toast from "react-hot-toast";

const getAllTags = (files: FileItem[]): string[] => {
    const allTags = files.flatMap(file => file.tags);
    return [...new Set(allTags)];
};

const Tags: React.FC = () => {
    // Components
    const{
        isFolder,

        folderData,

        tagFilter,
        setTagFilter
    } = useAppContext();

    const allFiles = folderData.flatMap(item => isFolder(item) ? item.children : [item]).filter(i => !isFolder(i)) as FileItem[];
    const uniqueTags = getAllTags(allFiles);

    // Tag Panel : Right Click
    const [textLabel, setTextLabel] = useState("Settings")
    const [isSettingsOpen, setIsSettingsOpen] = useState(false)
    const onTagClick = (tag: string) => {
        setTagFilter(tag)
        setTextLabel("Clear Filter")
    }
    // Tag Settings
    const [expandedTag, setExpandedTag] = useState<string | null>(null);
    const [tagName, setTagName] = useState("");
    const [tagColor, setTagColor] = useState("#3B82F6");
    // Tag Settings : Toaster
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [confirmAction, setConfirmAction] = useState<"save" | "delete" | null>(null);
    const handleConfirm = () => {
        if (confirmAction === "save") {
            toast.success("Tag saved successfully!");
            setExpandedTag(null);
        }
        else if(confirmAction === "delete") {
            toast.error("Tag deleted successfully!");
            setExpandedTag(null);
        }
        setShowConfirmModal(false);
    };

    return (
        <>
        <div className="mt-2 bg-white border border-blue-200 rounded shadow-sm text-xs">
            <div className="flex items-center justify-between px-2 py-1 bg-blue-400 text-white text-xs font-semibold rounded-t">
                <span>Tags</span>

                <Button
                    size="sm"
                    variant="ghost"
                    className="text-xs px-2 py-0.5 rounded-md font-medium hover:bg-blue-100 dark:hover:bg-blue-200 transition text-white hover:text-blue-900 h-6"
                    onPress={() => {
                        if (textLabel === "Settings") {
                            setIsSettingsOpen(true);
                        } else {
                            setTagFilter(null);
                            setTextLabel("Settings");
                        }
                    }}
                >
                    {textLabel}
                </Button>
            </div>

            <div className="p-2 max-h-32 overflow-y-auto flex flex-wrap gap-1">
                {uniqueTags.map(tag => (
                    <span
                        key={tag}
                        className={`text-[10px] 
                            ${tagFilter === tag
                            ? 'bg-blue-600 text-white'
                            : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        } rounded-full px-2 py-0.5 cursor-pointer`}
                        onClick={() => onTagClick(tag)}
                    >
                            {tag}
                        </span>
                ))}
            </div>
        </div>

        {/* Tag Settings */}
        {isSettingsOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50">
                <div className="bg-white rounded-xl p-6 w-[520px] shadow-xl mt-20">
                    <h2 className="font-semibold text-lg mb-3 text-center">Tags</h2>
                    <Divider className="mt-1 mb-4" />

                    <div className="flex flex-wrap -mx-1">
                        {uniqueTags.map((tag) => (
                            <div key={tag} className="w-1/5 px-1 mb-2">
                                    <span
                                        onClick={() => {
                                            setExpandedTag(tag === expandedTag ? null : tag);
                                            setTagName(tag);
                                        }}
                                        className="block bg-blue-100 text-blue-800 text-xs rounded px-2 py-1 text-center truncate cursor-pointer hover:bg-blue-200 transition"
                                    >
                                        {tag}
                                    </span>
                            </div>
                        ))}
                    </div>

                    {expandedTag && (
                        <div className="overflow-hidden transition-all duration-300 ease-in-out mt-4 border rounded-md p-4 bg-gray-50 shadow-sm">
                            <h3 className="font-semibold text-sm mb-3">
                                Edit Tag: <span style={{ color: tagColor }}>{tagName}</span>
                            </h3>

                            <div className="mb-3">
                                <label className="text-xs font-medium block mb-1">Color</label>
                                <div className="relative flex items-center gap-2">
                                    <input
                                        type="color"
                                        value={tagColor}
                                        onChange={(e) => setTagColor(e.target.value)}
                                        className="w-7 h-7 p-0 border border-gray-300 rounded cursor-pointer"
                                        style={{ appearance: "none" }}
                                    />

                                    <div className="relative flex items-center">
                                        <span className="absolute left-2 text-gray-500 select-none font-mono">#</span>
                                        <input
                                            type="text"
                                            value={tagColor.startsWith("#") ? tagColor.slice(1) : tagColor}
                                            onChange={(e) => {
                                                const val = e.target.value;
                                                const filtered = val.replace(/[^0-9a-fA-F]/g, "").slice(0, 6);
                                                setTagColor("#" + filtered);
                                            }}
                                            className="border pl-5 pr-2 py-1 rounded text-sm w-24 font-mono"
                                            placeholder="RRGGBB"
                                            maxLength={6}
                                        />
                                    </div>
                                </div>
                            </div>


                            <label className="text-xs font-medium block mb-1">Rename</label>
                            <input
                                type="text"
                                value={tagName}
                                onChange={(e) => setTagName(e.target.value)}
                                className="border px-2 py-1 rounded text-sm w-full mb-3"
                            />

                            <div className="flex justify-between items-center">
                                <Button
                                    size="sm"
                                    variant="light"
                                    className="text-red-500 hover:bg-red-100"
                                    onPress={() => {
                                        setConfirmAction("delete");
                                        setShowConfirmModal(true);
                                    }}
                                >
                                    Delete
                                </Button>

                                <Button
                                    size="sm"
                                    variant="solid"
                                    onPress={() => {
                                        setConfirmAction("save");
                                        setShowConfirmModal(true);
                                    }}
                                >
                                    Save
                                </Button>
                            </div>
                        </div>
                    )}
                    {/* Toaster Confirm Panel */}
                    {showConfirmModal && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white p-5 rounded shadow-lg max-w-sm w-full">
                                <h3 className="font-semibold text-lg mb-3">Are you sure?</h3>
                                <div className="flex justify-end gap-2">
                                    <button
                                        onClick={() => {setShowConfirmModal(false);}}
                                        className="px-3 py-1 rounded bg-gray-100 hover:bg-gray-200"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleConfirm}
                                        className={`px-3 py-1 rounded text-white ${
                                            confirmAction === "delete" ? "bg-red-600 hover:bg-red-700" : "bg-green-600 hover:bg-green-700"
                                        }`}
                                    >
                                        Confirm
                                    </button>
                                </div>
                            </div>
                        </div>)}

                    <div className="mt-6 flex justify-end">
                        <Button
                            size="sm"
                            variant="light"
                            onPress={() => {
                                setIsSettingsOpen(false);
                                setExpandedTag(null);
                            }}
                            className="border border-gray-300 rounded-md px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 transition"
                        >
                            Close
                        </Button>
                    </div>
                </div>
            </div>
        )}
        </>
    )
}

export default Tags;