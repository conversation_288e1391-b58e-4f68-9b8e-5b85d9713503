// src/api/auth-api.ts

const BASE_URL = "http://5.133.102.28:8000/api/v1";

export async function getMe(token: string) {
    const res = await fetch(`${BASE_URL}/auth/me`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });

    if (!res.ok) {
        const err = await res.json();
        throw new Error(err.message || "Profil alınamadı.");
    }

    return res.json();
}

export async function signin(credentials: { email: string; password: string }) {
    const response = await fetch("http://5.133.102.28:8000/api/v1/auth/signin", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
    });

    const result = await response.json();

    if (!response.ok) {
        throw new Error(result.message || "<PERSON><PERSON><PERSON> başarısız");
    }

    return result; // içinde: accessToken + user
}

export async function getCurrentUser(token: string) {
    const res = await fetch("http://5.133.102.28:8000/api/v1/user/current", {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    });

    const result = await res.json();

    if (!res.ok) {
        throw new Error(result.message || "Kullanıcı bilgisi alınamadı");
    }

    return result;
}

export async function signup(data: any) {
    const res = await fetch("http://5.133.102.28:8000/api/v1/auth/signup", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    });

    const result = await res.json();
    if (!res.ok) {
        throw new Error(result.message || "Kayıt başarısız");
    }

    return result;
}