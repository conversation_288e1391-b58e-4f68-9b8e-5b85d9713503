import React from "react";

import {Toaster} from "react-hot-toast";

// Import Panels
import Folders from "./folders.tsx";
import Groups from "./groups.tsx";
import Tags from "./tags.tsx";


const LeftPanel: React.FC = () => {
    return (
        <div className="flex flex-col h-full w-full text-xs overflow-hidden">
            <Toaster />

            {/* Folders && Groups */}
            <div className="flex-1 overflow-y-auto">
                <Folders />
                <Groups />
            </div>

            {/* Tags */}
            <div className="border-t mt-2">
                <Tags />
            </div>
        </div>
    );
}

export default LeftPanel;