// Enhanced data structures
export interface FileItem {
    id: number;
    name: string;
    type: string;
    tags: string[];
    title?: string;
    creator?: string;
    itemType?: string;
    year?: string;
    series: string;
    edition: string;
    language: string;
    isbn: string;
    url: string;
    pages: string[];
    dateAdded: string;
    modified: string;
    abstract: string;
}

export interface FolderItem {
    id: number;
    name: string;
    type: 'folder';
    children: (FileItem | FolderItem)[];
    isExpanded?: boolean;
}

export type TreeItem = FileItem | FolderItem;

// Enhanced folder structure with more metadata
export const folderStructure: TreeItem[] = [
    {
        id: 1,
        name: 'Machine Learning',
        type: 'folder',
        isExpanded: true,
        children: [
            {
                id: 2,
                name: 'Machine Learning Fundamentals.pdf',
                type: 'pdf',
                tags: ['AI', 'ML', 'Education'],
                title: 'Machine Learning Fundamentals',
                creator: '<PERSON>',
                itemType: 'Book',
                year: "2023",
                series: '-',
                edition: '2nd',
                language: 'English',
                isbn: '9780134685991',
                url: 'https://example.com/ml-fundamentals',
                pages: ['1-250'],
                dateAdded: '2024-01-15',
                modified: '2024-01-18',
                abstract: 'An introductory guide to fundamental machine learning concepts.'
            },
            {
                id: 3,
                name: 'React Best Practices.pdf',
                type: 'pdf',
                tags: ['Frontend', 'React', 'JavaScript'],
                title: 'React Best Practices',
                creator: 'Dan Abramov',
                itemType: 'Article',
                year: "2024",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: 'https://example.com/react-best-practices',
                pages: ['1-50'],
                dateAdded: '2024-02-10',
                modified: '2024-02-11',
                abstract: 'Best practices and patterns for building robust React applications.'
            },
            {
                id: 4,
                name: 'Deep Learning',
                type: 'folder',
                isExpanded: false,
                children: [
                    {
                        id: 5,
                        name: 'Deep Learning Research.pdf',
                        type: 'pdf',
                        tags: ['AI', 'Deep Learning', 'Research'],
                        title: 'Deep Learning Research',
                        creator: 'Geoffrey Hinton',
                        itemType: 'Research Paper',
                        year: "2022",
                        series: 'Nature Research',
                        edition: '-',
                        language: 'English',
                        isbn: '-',
                        url: 'https://example.com/deep-learning-research',
                        pages: ['1-80'],
                        dateAdded: '2024-01-20',
                        modified: '2024-01-21',
                        abstract: 'Key research developments in deep learning by Geoffrey Hinton.'
                    },
                    {
                        id: 6,
                        name: 'Neural Networks Guide.pdf',
                        type: 'pdf',
                        tags: ['AI', 'Neural Networks', 'Deep Learning'],
                        title: 'Neural Networks Guide',
                        creator: 'Yann LeCun',
                        itemType: 'Book',
                        year: "2023",
                        series: 'AI Master Series',
                        edition: '1st',
                        language: 'English',
                        isbn: '9780262035613',
                        url: '-',
                        pages: ['1-300'],
                        dateAdded: '2024-01-25',
                        modified: '2024-01-25',
                        abstract: 'Comprehensive guide to neural networks and their architectures.'
                    },
                    {
                        id: 7,
                        name: 'Computer Vision.pdf',
                        type: 'pdf',
                        tags: ['AI', 'Computer Vision', 'Deep Learning'],
                        title: 'Computer Vision',
                        creator: 'Fei-Fei Li',
                        itemType: 'Book',
                        year: "2023",
                        series: '-',
                        edition: '-',
                        language: 'English',
                        isbn: '9780128046029',
                        url: 'https://example.com/computer-vision',
                        pages: ['1-220'],
                        dateAdded: '2024-02-01',
                        modified: '2024-02-02',
                        abstract: 'A modern introduction to computer vision techniques and applications.'
                    },
                ]
            },
        ]
    },
    {
        id: 8,
        name: 'Web Development',
        type: 'folder',
        isExpanded: false,
        children: [
            {
                id: 9,
                name: 'TypeScript Guide.pdf',
                type: 'pdf',
                tags: ['TypeScript', 'Programming'],
                title: 'TypeScript Guide',
                creator: 'Anders Hejlsberg',
                itemType: 'Documentation',
                year: "2024",
                series: '-',
                edition: '1st',
                language: 'English',
                isbn: '9781492037651',
                url: 'https://example.com/typescript-guide',
                pages: ['1-180'],
                dateAdded: '2024-02-15',
                modified: '2024-02-16',
                abstract: 'Comprehensive documentation for mastering TypeScript.'
            },
            {
                id: 10,
                name: 'Node.js Architecture.pdf',
                type: 'pdf',
                tags: ['Backend', 'Node.js', 'JavaScript'],
                title: 'Node.js Architecture',
                creator: 'Ryan Dahl',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: '-',
                pages: ['1-60'],
                dateAdded: '2024-02-20',
                modified: '2024-02-20',
                abstract: 'An overview of the architectural decisions behind Node.js.'
            },
            {
                id: 11,
                name: 'API Design Guidelines.pdf',
                type: 'pdf',
                tags: ['API', 'Design', 'REST'],
                title: 'API Design Guidelines',
                creator: 'Roy Fielding',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: 'https://example.com/api-guidelines',
                pages: ['1-45'],
                dateAdded: '2024-03-01',
                modified: '2024-03-01',
                abstract: 'RESTful API design principles and best practices.'
            },
        ]
    },
    {
        id: 12,
        name: 'Database',
        type: 'folder',
        isExpanded: false,
        children: [
            {
                id: 13,
                name: 'Database Design Patterns.pdf',
                type: 'pdf',
                tags: ['Database', 'Design', 'Patterns'],
                title: 'Database Design Patterns',
                creator: 'Martin Fowler',
                itemType: 'Book',
                year: "2022",
                series: 'DB Pro Series',
                edition: '1st',
                language: 'English',
                isbn: '9780321127426',
                url: '-',
                pages: ['1-240'],
                dateAdded: '2024-03-05',
                modified: '2024-03-06',
                abstract: 'Common design patterns for relational databases.'
            },
            {
                id: 14,
                name: 'SQL Optimization.pdf',
                type: 'pdf',
                tags: ['Database', 'SQL', 'Performance'],
                title: 'SQL Optimization',
                creator: 'Bill Karwin',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: 'https://example.com/sql-optimization',
                pages: ['1-35'],
                dateAdded: '2024-03-10',
                modified: '2024-03-10',
                abstract: 'Tips and techniques for writing efficient SQL queries.'
            },
        ]
    },
    {
        id: 15,
        name: 'DevOps',
        type: 'folder',
        isExpanded: false,
        children: [
            {
                id: 16,
                name: 'Cloud Computing Overview.pdf',
                type: 'pdf',
                tags: ['Cloud', 'AWS', 'Azure'],
                title: 'Cloud Computing Overview',
                creator: 'Werner Vogels',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: '-',
                pages: ['1-40'],
                dateAdded: '2024-03-15',
                modified: '2024-03-15',
                abstract: 'A high-level overview of cloud computing concepts and services.'
            },
            {
                id: 17,
                name: 'DevOps Handbook.pdf',
                type: 'pdf',
                tags: ['DevOps', 'CI/CD', 'Deployment'],
                title: 'DevOps Handbook',
                creator: 'Gene Kim',
                itemType: 'Book',
                year: "2022",
                series: 'DevOps Series',
                edition: '2nd',
                language: 'English',
                isbn: '9781942788003',
                url: 'https://example.com/devops-handbook',
                pages: ['1-350'],
                dateAdded: '2024-03-20',
                modified: '2024-03-22',
                abstract: 'Best practices for implementing DevOps culture and pipelines.'
            },
            {
                id: 18,
                name: 'Security Best Practices.pdf',
                type: 'pdf',
                tags: ['Security', 'Cybersecurity'],
                title: 'Security Best Practices',
                creator: 'Bruce Schneier',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: 'https://example.com/security-practices',
                pages: ['1-50'],
                dateAdded: '2024-03-25',
                modified: '2024-03-25',
                abstract: 'Key practices to enhance software and system security.'
            },
            {
                id: 19,
                name: 'Git Workflow Guide.docx',
                type: 'pdf',
                tags: ['Git', 'Version Control'],
                title: 'Git Workflow Guide',
                creator: 'Scott Chacon',
                itemType: 'Documentation',
                year: "2023",
                series: '-',
                edition: '1st',
                language: 'English',
                isbn: '-',
                url: '-',
                pages: ['1-30'],
                dateAdded: '2024-04-01',
                modified: '2024-04-01',
                abstract: 'Practical guide for managing Git workflows.'
            },
            {
                id: 20,
                name: 'Performance Optimization.docx',
                type: 'docx',
                tags: ['Performance', 'Optimization'],
                title: 'Performance Optimization',
                creator: 'Steve Souders',
                itemType: 'Article',
                year: "2023",
                series: '-',
                edition: '-',
                language: 'English',
                isbn: '-',
                url: '-',
                pages: ['1-25'],
                dateAdded: '2024-04-05',
                modified: '2024-04-06',
                abstract: 'Strategies for optimizing web performance and load times.'
            }
        ]
    },
    {
        id: 21,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 22,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 23,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 24,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 25,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 26,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 27,
        name: 'Test Folder',
        type: 'folder',
        isExpanded: false,
        children: [],
    },
    {
        id: 28,
        name: 'Trash',
        type: 'folder',
        isExpanded: false,
        children: [],
    }
];