import {
    DocumentIcon,
    FolderIcon,
    TrashIcon,
} from '@heroicons/react/24/outline';
import type { TreeItem } from '../data/user-folder.ts';

export const iconForItem = (item: TreeItem, className = 'h-4 w-4') => {
    if (item.type === 'folder') {
        if (item.name.toLowerCase() === 'trash') {
            return <TrashIcon className={`${className} text-red-500`} />;
        }
        return <FolderIcon className={`${className} text-gray-600`} />;
    }

    const lowerName = item.name.toLowerCase();
    if (lowerName.endsWith('.pdf')) return <DocumentIcon className={`${className} text-red-500`} />;
    if (lowerName.endsWith('.doc') || lowerName.endsWith('.docx')) return <DocumentIcon className={`${className} text-blue-600`} />;
    if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) return <DocumentIcon className={`${className} text-green-600`} />;

    return <DocumentIcon className={`${className} text-gray-400`} />;
};
