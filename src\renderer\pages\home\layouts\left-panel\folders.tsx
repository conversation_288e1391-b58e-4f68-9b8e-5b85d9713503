import React, {type JSX, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@heroui/react";
import {
    ChartBarIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    DocumentArrowDownIcon,
    DocumentTextIcon,
    PencilIcon,
    PlusIcon,
    TrashIcon
} from "@heroicons/react/24/outline";
import {useAppContext} from '../../../../components/context/use-app-context.ts';
import type {TreeItem} from "../../../../data/user-folder.ts";
import {iconForItem} from "../../../../utils/icon-util.tsx";
import {isFolder} from "../../../../utils/panel-util.ts";
import ContextMenu, {type ContextMenuSection} from "../../../../components/context-menu.tsx";

const Folders: React.FC = () => {
    const {
        selectedFolderId,
        setSelectedF<PERSON>erId,

        setSelectedFile,

        folderData,
        setFolderData,

    } = useAppContext();

    // Context Menu: Right Click
    const [contextMenu, setContextMenu] = useState<{x: number; y: number; sections: ContextMenuSection[]} | null>(null);
    const handleRightClick = (
        e: React.MouseEvent,
        folder: TreeItem,
    ) => {
        e.preventDefault();
        if (!isFolder(folder)) return;

        const sections = [
            {
                items: [
                    {
                        label: "New Subcollection",
                        onClick: () => console.log("New subcollection", folder.id),
                        icon: <PlusIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Rename Collection",
                        onClick: () => console.log("Rename", folder.id),
                        icon: <PencilIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Export Collection",
                        onClick: () => console.log("Export", folder.id),
                        icon: <DocumentArrowDownIcon className="w-4 h-4" />
                    },
                    {
                        label: "Create Bibliography",
                        onClick: () => console.log("Bibliography", folder.id),
                        icon: <DocumentTextIcon className="w-4 h-4" />
                    },
                    {
                        label: "Generate Report",
                        onClick: () => console.log("Report", folder.id),
                        icon: <ChartBarIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Delete Collection",
                        onClick: () => console.log("Delete collection", folder.id),
                        icon: <TrashIcon className="w-4 h-4" />
                    },
                    {
                        label: "Delete Collection and Items",
                        onClick: () => console.log("Delete all", folder.id),
                        icon: <TrashIcon className="w-4 h-4" />
                    }
                ]
            }
        ];

        setContextMenu({
            x: e.clientX,
            y: e.clientY,
            sections
        });
    }

    const toggleFolder = (folderId: number) => {
        const updateFolderExpansion = (items: TreeItem[]): TreeItem[] => {
            return items.map(item => {
                if (isFolder(item)) {
                    if (item.id === folderId) {
                        return { ...item, isExpanded: !item.isExpanded };
                    } else {
                        return { ...item, children: updateFolderExpansion(item.children) };
                    }
                }
                return item;
            });
        };
        setFolderData(updateFolderExpansion(folderData));
    };

    const renderTreeItem = (item: TreeItem, level = 0): JSX.Element | null => {
        if (!isFolder(item)) return null;

        const paddingLeft = level * 14;
        const hasSubfolders = item.children.some(child => isFolder(child));

        return (
            <div key={item.id}>
                <div
                    className={`flex items-center gap-1.5 p-1 rounded cursor-pointer text-xs transition-all duration-100
                    ${selectedFolderId === item.id ? 'bg-blue-100 border border-blue-300 shadow-sm' : 'hover:bg-gray-100'}`}
                    style={{ paddingLeft }}
                    onClick={() => {
                        toggleFolder(item.id);
                        setSelectedFolderId(item.id);
                        setSelectedFile(null);
                    }}
                    onContextMenu={(e) => handleRightClick(e, item)}
                >
                    {hasSubfolders && (
                        item.isExpanded
                            ? <ChevronDownIcon className="h-3 w-3 text-gray-500" />
                            : <ChevronRightIcon className="h-3 w-3 text-gray-500" />
                    )}
                    {iconForItem(item)}
                    <span className="truncate">{item.name}</span>
                </div>
                {item.isExpanded && (
                    <div>{item.children.map(child => renderTreeItem(child, level + 1))}</div>
                )}
            </div>
        );
    };

    return (
        <>
            <div className="flex items-center justify-between px-3 py-2">
                <Button
                    size="sm"
                    variant="light"
                    className="text-sm font-semibold flex items-center justify-center h-8 leading-5 pt-1"
                    onPress={() => {
                        setSelectedFolderId(null);
                        setSelectedFile(null);
                    }}
                >
                    Library
                </Button>

                <Tooltip content="New Folder">
                    <Button isIconOnly variant="ghost" className="p-1 h-5 w-5 min-w-0">
                        <PlusIcon className="h-3 w-3" />
                    </Button>
                </Tooltip>
            </div>

            <div className="flex-1 overflow-y-auto space-y-2">
                {/* Show All Files */}
                <div className="px-3 py-2 space-y-1">
                    {folderData.map(item => renderTreeItem(item))}
                </div>
            </div>

            {contextMenu && (
                <ContextMenu
                    x={contextMenu.x}
                    y={contextMenu.y}
                    sections={contextMenu.sections}
                    onClose={() => setContextMenu(null)}
                />
            )}
        </>
    )
}

export default Folders;