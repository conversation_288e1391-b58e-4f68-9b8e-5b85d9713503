// Resize Right Panel ( Close or Open )
import React, { useState, useEffect, useRef } from "react";
import { RightPanel } from "../../pages/home/<USER>/right-panel/right-panel.tsx";
import { ChevronLeftIcon } from "@heroicons/react/24/solid";

const ResizableRightPanel: React.FC = () => {
    const [panelWidth, setPanelWidth] = useState(350);
    const [isResizing, setIsResizing] = useState(false);
    const [isOpen, setIsOpen] = useState(true);
    const panelRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (!isResizing) return;

            const newWidth = window.innerWidth - e.clientX;
            const minWidth = 250;
            const maxWidth = 400;

            if (newWidth < minWidth) {
                setIsOpen(false);
                setPanelWidth(0);
            } else if (newWidth <= maxWidth) {
                setPanelWidth(newWidth);
            }
        };

        const stopResizing = () => setIsResizing(false);

        if (isResizing) {
            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", stopResizing);
        }

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", stopResizing);
        };
    }, [isResizing]);

    if (!isOpen) {
        return (
            <div
                className="w-4 h-full bg-blue-100 border-l border-blue-300 flex items-center justify-center cursor-pointer hover:bg-blue-200"
                onClick={() => {
                    setIsOpen(true);
                    setPanelWidth(280);
                }}
            >
                <ChevronLeftIcon className="w-4 h-4 text-blue-700" />
            </div>
        );
    }

    return (
        <div
            ref={panelRef}
            style={{ width: panelWidth }}
            className="relative h-full flex-shrink-0 border-l bg-white"
        >
            <RightPanel />

            <div
                className={`absolute top-0 left-0 w-1 h-full cursor-col-resize transition duration-200 ${
                    isResizing ? "bg-blue-400" : "bg-transparent hover:bg-blue-300"
                }`}
                onMouseDown={() => setIsResizing(true)}
            />

            {panelWidth <= 70 && (
                <div
                    className="absolute right-1 top-1 cursor-pointer text-blue-500 hover:text-blue-700 text-xs"
                    onClick={() => {
                        setIsOpen(false);
                        setPanelWidth(0);
                    }}
                >
                    Kapat
                </div>
            )}
        </div>
    );
};

export default ResizableRightPanel;
