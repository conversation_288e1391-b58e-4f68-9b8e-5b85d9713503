import React, {useState, useEffect, useRef} from "react";
import { useAppContext } from "../../../../components/context/use-app-context.ts";

import {
    Button,
    Tooltip,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    DropdownSection,
    Input,
} from "@heroui/react";
import {
    DocumentIcon,
    PlusIcon,
    FolderIcon,
    BookOpenIcon,
    PencilIcon,
    MagnifyingGlassIcon,
    GlobeAltIcon,
    EyeIcon,
    PaperClipIcon,
    MagnifyingGlassPlusIcon,
    FolderPlusIcon,
    TrashIcon,
    DocumentArrowDownIcon,
    DocumentTextIcon,
    ChartBarIcon,
    DocumentDuplicateIcon,
    BookmarkIcon
} from "@heroicons/react/24/outline";

import type {FileItem, FolderItem, TreeItem} from "../../../../data/user-folder.ts";
import ContextMenu, { type ContextMenuSection } from "../../../../components/context-menu.tsx";

const MiddlePanel: React.FC = () => {
    const{
        isFolder,

        folderData,

        selectedFolderId,

        selectedFile,
        setSelectedFile,

        tagFilter,
    } = useAppContext();

    const [topContextMenu, setTopContextMenu] = useState<{ x: number; y: number } | null>(null);
    const [rowContextMenu, setRowContextMenu] = useState<{x: number; y: number; sections: ContextMenuSection[]} | null>(null);
    const menuRef = useRef<HTMLDivElement>(null);

    const handleContextMenu = (e: React.MouseEvent) => {
        e.preventDefault();
        setTopContextMenu({ x: e.pageX, y: e.pageY });
    };

    const handleRowRightClick = (e: React.MouseEvent, file: FileItem) => {
        e.preventDefault();
        e.stopPropagation();

        const sections: ContextMenuSection[] = [
            {
                items: [
                    {
                        label: "View Online",
                        onClick: () => console.log("View online", file.id),
                        icon: <GlobeAltIcon className="w-4 h-4" />
                    },
                    {
                        label: "Show in Library",
                        onClick: () => console.log("Show in library", file.id),
                        icon: <EyeIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Add Note",
                        onClick: () => console.log("Add note", file.id),
                        icon: <PencilIcon className="w-4 h-4" />
                    },
                    {
                        label: "Add Attachment",
                        onClick: () => console.log("Add attachment", file.id),
                        icon: <PaperClipIcon className="w-4 h-4" />
                    },
                    {
                        label: "Find Full Text",
                        onClick: () => console.log("Find full text", file.id),
                        icon: <MagnifyingGlassPlusIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Add to Collection",
                        onClick: () => console.log("Add to collection", file.id),
                        icon: <FolderPlusIcon className="w-4 h-4" />
                    },
                    {
                        label: "Remove Item from Collection",
                        onClick: () => console.log("Remove from collection", file.id),
                        icon: <FolderIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Create Book Section",
                        onClick: () => console.log("Create book section", file.id),
                        icon: <BookmarkIcon className="w-4 h-4" />
                    },
                    {
                        label: "Duplicate Item",
                        onClick: () => console.log("Duplicate item", file.id),
                        icon: <DocumentDuplicateIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Export Item",
                        onClick: () => console.log("Export item", file.id),
                        icon: <DocumentArrowDownIcon className="w-4 h-4" />
                    },
                    {
                        label: "Create Bibliography from Item",
                        onClick: () => console.log("Create bibliography", file.id),
                        icon: <DocumentTextIcon className="w-4 h-4" />
                    },
                    {
                        label: "Generate Report from Item",
                        onClick: () => console.log("Generate report", file.id),
                        icon: <ChartBarIcon className="w-4 h-4" />
                    }
                ]
            },
            {
                items: [
                    {
                        label: "Move Item to Trash",
                        onClick: () => console.log("Move to trash", file.id),
                        icon: <TrashIcon className="w-4 h-4" />
                    }
                ]
            }
        ];

        setRowContextMenu({
            x: e.clientX,
            y: e.clientY,
            sections
        });
    };

    const [visibleColumns, setVisibleColumns] = useState({
        name: true,
        creator: true,
        type: true,
        itemType: true,
        year: true,
        series: true,
        edition: true,
        language: true,
        ISBN: true,
        URL: true,
        pages: true,
        dateAdded: true,
        modified: true,
        tags: true,
    });

    const getFilesInFolder = (items: TreeItem[], folderId: number | null): FileItem[] => {
        if (folderId === null) {
            const flatten = (nodes: TreeItem[]): FileItem[] => {
                return nodes.flatMap(item => {
                    if (isFolder(item)) return flatten(item.children);
                    return [item as FileItem];
                });
            };
            return flatten(items);
        }

        const findFolder = (items: TreeItem[]): FolderItem | null => {
            for (const item of items) {
                if (isFolder(item)) {
                    if (item.id === folderId) return item;
                    const found = findFolder(item.children);
                    if (found) return found;
                }
            }
            return null;
        };

        const folder = findFolder(items);
        if (!folder) return [];
        return folder.children.filter(child => !isFolder(child)) as FileItem[];
    };

    const [searchQuery] = useState('');
    const filesInFolder = getFilesInFolder(folderData, selectedFolderId);
    const filteredFiles = filesInFolder.filter(file => {
        const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesTag = tagFilter ? file.tags.includes(tagFilter) : true;
        return matchesSearch && matchesTag;
    });

    const toggleColumn = (column: keyof typeof visibleColumns) => {
        setVisibleColumns(prev => ({ ...prev, [column]: !prev[column] }));
    };

    const closeTopContextMenu = () => {
        setTopContextMenu(null);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (!topContextMenu) return;

            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                closeTopContextMenu();
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => document.removeEventListener("click", handleClickOutside);
    }, [topContextMenu]);

    // Top Buttons
    const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

    return (
        <div className="flex-1 bg-white px-3 py-1 flex flex-col overflow-hidden min-w-0">
            <div className="text-sm font-semibold flex items-center justify-center h-8 leading-5 pt-1">Articles</div>

            {/* Buttons */}
            <div className="flex justify-between items-center mb-2 gap-2">
                <div className="flex items-center gap-1 flex-shrink-0">

                    {/* New Items */}
                    <Tooltip content="New Item" placement="top" closeDelay={2}>
                        <div>
                            <Dropdown
                                placement="bottom-start"
                                isOpen={activeDropdown === "newItem"}
                                onOpenChange={(open) => setActiveDropdown(open ? "newItem" : null)}
                            >
                                <DropdownTrigger>
                                    <Button size="sm" isIconOnly variant="light" className="p-1">
                                        <PlusIcon className="h-4 w-4" />
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    aria-label="New Item Types"
                                    className="text-xs min-w-[260px] max-h-[350px] overflow-y-auto"
                                    variant="flat"
                                >
                                    <DropdownSection title="Common Types">
                                        <DropdownItem key="artwork">Artwork</DropdownItem>
                                        <DropdownItem key="bill">Bill</DropdownItem>
                                        <DropdownItem key="book">Book</DropdownItem>
                                        <DropdownItem key="bookSection">Book Section</DropdownItem>
                                        <DropdownItem key="hearing">Hearing</DropdownItem>
                                    </DropdownSection>

                                    <DropdownSection title="Media & Communication">
                                        <DropdownItem key="audioRecording">Audio Recording</DropdownItem>
                                        <DropdownItem key="blogPost">Blog Post</DropdownItem>
                                        <DropdownItem key="film">Film</DropdownItem>
                                        <DropdownItem key="forumPost">Forum Post</DropdownItem>
                                        <DropdownItem key="instantMessage">Instant Message</DropdownItem>
                                        <DropdownItem key="interview">Interview</DropdownItem>
                                        <DropdownItem key="podcast">Podcast</DropdownItem>
                                        <DropdownItem key="radioBroadcast">Radio Broadcast</DropdownItem>
                                        <DropdownItem key="tvBroadcast">TV Broadcast</DropdownItem>
                                        <DropdownItem key="videoRecording">Video Recording</DropdownItem>
                                    </DropdownSection>

                                    <DropdownSection title="Academic & Technical">
                                        <DropdownItem key="case">Case</DropdownItem>
                                        <DropdownItem key="conferencePaper">Conference Paper</DropdownItem>
                                        <DropdownItem key="dataset">Dataset</DropdownItem>
                                        <DropdownItem key="dictionaryEntry">Dictionary Entry</DropdownItem>
                                        <DropdownItem key="document">Document</DropdownItem>
                                        <DropdownItem key="email">E-mail</DropdownItem>
                                        <DropdownItem key="encyclopediaArticle">Encyclopedia Article</DropdownItem>
                                        <DropdownItem key="journalArticle">Journal Article</DropdownItem>
                                        <DropdownItem key="letter">Letter</DropdownItem>
                                        <DropdownItem key="magazineArticle">Magazine Article</DropdownItem>
                                        <DropdownItem key="manuscript">Manuscript</DropdownItem>
                                        <DropdownItem key="map">Map</DropdownItem>
                                        <DropdownItem key="newspaperArticle">Newspaper Article</DropdownItem>
                                        <DropdownItem key="patent">Patent</DropdownItem>
                                        <DropdownItem key="preprint">Preprint</DropdownItem>
                                        <DropdownItem key="presentation">Presentation</DropdownItem>
                                        <DropdownItem key="report">Report</DropdownItem>
                                        <DropdownItem key="software">Software</DropdownItem>
                                        <DropdownItem key="standard">Standard</DropdownItem>
                                        <DropdownItem key="statute">Statute</DropdownItem>
                                        <DropdownItem key="thesis">Thesis</DropdownItem>
                                    </DropdownSection>
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </Tooltip>

                    {/* New Items with doi etc... id */}
                    <Tooltip content="Add Item(s) by Identifier" placement="top" closeDelay={2}>
                        <div>
                            <Dropdown
                                placement="bottom-start"
                                content="Add Item(s) by Identifier"
                                isOpen={activeDropdown === "addById"}
                                onOpenChange={(open) => setActiveDropdown(open ? "addById" : null)}
                            >
                                <DropdownTrigger>
                                    <Button size="sm" isIconOnly variant="light" className="p-1">
                                        <DocumentIcon className="h-4 w-4" />
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    aria-label="Add by Identifier"
                                    className="text-xs min-w-[300px] max-w-[500px]"
                                    variant="flat"
                                    closeOnSelect={false}
                                >
                                    <DropdownItem
                                        isReadOnly
                                        key="info"
                                        className="pointer-events-none text-wrap normal-case text-left font-normal text-gray-700 whitespace-normal"
                                    >
                                        Enter ISBNs, DOIs, PMIDs, arXiv IDs or ADS Bibcodes to add to your library:
                                    </DropdownItem>
                                    <DropdownItem key="input" isReadOnly>
                                        <input
                                            type="text"
                                            className="w-full px-2 py-1 border border-gray-300 rounded text-xs"
                                            placeholder="e.g. 978-3-16-148410-0"
                                        />
                                    </DropdownItem>
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </Tooltip>

                    <Tooltip content="Add Attachment" placement="top" closeDelay={2}>
                        <div>
                            <Dropdown
                                placement="bottom-start"
                                isOpen={activeDropdown === "addAttachment"}
                                onOpenChange={(open) => setActiveDropdown(open ? "addAttachment" : null)}
                            >
                                <DropdownTrigger>
                                    <Button size="sm" isIconOnly variant="light" className="p-1">
                                        <FolderIcon className="h-4 w-4" />
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    aria-label="Add Attachment"
                                    className="text-xs min-w-[180px]"
                                    variant="flat"
                                >
                                    <DropdownSection showDivider title="Add from Computer">
                                        <DropdownItem key="artwork">Add File...</DropdownItem>
                                        <DropdownItem key="book">Add Link to File...</DropdownItem>
                                    </DropdownSection>
                                    <DropdownSection showDivider title="Attach Existing">
                                        <DropdownItem key="bookSection">Attach File...</DropdownItem>
                                        <DropdownItem key="journalArticle">Attach Link to File</DropdownItem>
                                        <DropdownItem key="newspaperArticle">Attach Web Link</DropdownItem>
                                    </DropdownSection>
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </Tooltip>

                    <Tooltip content="New Note" placement="top" closeDelay={2}>
                        <div>
                            <Dropdown
                                placement="bottom-start"
                                isOpen={activeDropdown === "addNote"}
                                onOpenChange={(open) => setActiveDropdown(open ? "addNote" : null)}
                            >
                                <DropdownTrigger>
                                    <Button size="sm" isIconOnly variant="light" className="p-1">
                                        <BookOpenIcon className="h-4 w-4" />
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    aria-label="Add Note"
                                    className="text-xs min-w-[220px]"
                                    variant="flat"
                                >
                                    <DropdownSection title="Note Options">
                                        <DropdownItem
                                            key="standaloneNote"
                                            description="Create a note not attached to any item"
                                            startContent={<PencilIcon className="h-4 w-4 text-yellow-600" />}
                                        >
                                            New Standalone Note
                                        </DropdownItem>
                                        <DropdownItem
                                            key="itemNote"
                                            description="Attach a note to the selected item"
                                            startContent={<DocumentIcon className="h-4 w-4 text-gray-500" />}
                                        >
                                            New Item Note
                                        </DropdownItem>
                                    </DropdownSection>
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </Tooltip>
                </div>

                {/* Search Input */}
                <div className="flex-1 flex justify-end min-w-0">
                    <Input
                        placeholder="Search..."
                        startContent={<MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />}
                        size="sm"
                        className="w-full max-w-xs min-w-0"
                    />
                </div>
            </div>

            <div className="flex-1 overflow-hidden">
                <div className="overflow-x-auto overflow-y-auto h-full">
                    <table className="w-full table-auto text-xs min-w-max border-separate border-spacing-y-1">
                        <thead onContextMenu={handleContextMenu} className="text-gray-500 bg-gray-50 rounded-md">
                        <tr className="text-left border-b">
                            {visibleColumns.name && <th className="py-1 px-2 min-w-[120px]">Name</th>}
                            {visibleColumns.creator && <th className="py-1 px-2 min-w-[100px]">Creator</th>}
                            {visibleColumns.type && <th className="py-1 px-2 min-w-[60px]">Type</th>}
                            {visibleColumns.itemType && <th className="py-1 px-2 min-w-[80px]">Item Type</th>}
                            {visibleColumns.year && <th className="py-1 px-2 min-w-[60px]">Year</th>}
                            {visibleColumns.series && <th className="py-1 px-2 min-w-[80px]">Series</th>}
                            {visibleColumns.edition && <th className="py-1 px-2 min-w-[70px]">Edition</th>}
                            {visibleColumns.language && <th className="py-1 px-2 min-w-[80px]">Language</th>}
                            {visibleColumns.ISBN && <th className="py-1 px-2 min-w-[100px]">ISBN</th>}
                            {visibleColumns.URL && <th className="py-1 px-2 min-w-[100px]">URL</th>}
                            {visibleColumns.pages && <th className="py-1 px-2 min-w-[60px]">Pages</th>}
                            {visibleColumns.dateAdded && <th className="py-1 px-2 min-w-[90px]">Date Added</th>}
                            {visibleColumns.modified && <th className="py-1 px-2 min-w-[90px]">Modified</th>}
                            {visibleColumns.tags && <th className="py-1 px-2 min-w-[120px]">Tags</th>}
                        </tr>
                        </thead>
                        <tbody>
                        {filteredFiles.map(file => (
                            <tr
                                key={file.id}
                                onClick={() => setSelectedFile(file)}
                                onContextMenu={(e) => handleRowRightClick(e, file)}
                                className={`cursor-pointer hover:bg-gray-50 ${
                                    selectedFile?.id === file.id ? 'bg-blue-50' : ''
                                }`}
                            >
                                {visibleColumns.name && <td className="py-1 px-2 min-w-[120px] truncate">{file.name}</td>}
                                {visibleColumns.creator && <td className="py-1 px-2 min-w-[100px] truncate">{file.creator}</td>}
                                {visibleColumns.type && <td className="py-1 px-2 min-w-[60px] truncate">{file.type?.toUpperCase()}</td>}
                                {visibleColumns.itemType && <td className="py-1 px-2 min-w-[80px] truncate">{file.itemType}</td>}
                                {visibleColumns.year && <td className="py-1 px-2 min-w-[60px] truncate">{file.year}</td>}
                                {visibleColumns.series && <td className="py-1 px-2 min-w-[80px] truncate">{file.series?.toUpperCase()}</td>}
                                {visibleColumns.edition && <td className="py-1 px-2 min-w-[70px] truncate">{file.edition?.toUpperCase()}</td>}
                                {visibleColumns.language && <td className="py-1 px-2 min-w-[80px] truncate">{file.language}</td>}
                                {visibleColumns.ISBN && <td className="py-1 px-2 min-w-[100px] truncate">{file.isbn}</td>}
                                {visibleColumns.URL && <td className="py-1 px-2 min-w-[100px] truncate">{file.url}</td>}
                                {visibleColumns.pages && <td className="py-1 px-2 min-w-[60px] truncate">{file.pages}</td>}
                                {visibleColumns.dateAdded && <td className="py-1 px-2 min-w-[90px] truncate">{file.dateAdded?.toUpperCase()}</td>}
                                {visibleColumns.modified && <td className="py-1 px-2 min-w-[90px] truncate">{file.modified?.toUpperCase()}</td>}
                                {visibleColumns.tags && (
                                    <td className="py-1 px-2 min-w-[120px]">
                                        <div className="flex flex-wrap gap-1">
                                            {file.tags.map(tag => (
                                                <span key={tag} className="bg-gray-200 text-gray-600 rounded px-2 py-0.5 text-[10px] whitespace-nowrap">
                                                    {tag}
                                                </span>
                                            ))}
                                        </div>
                                    </td>
                                )}
                            </tr>
                        ))}
                        </tbody>
                    </table>
                </div>

                {/* Header Context Menu */}
                {topContextMenu && (
                    <div
                        ref={menuRef}
                        className="absolute z-50 bg-white border border-gray-300 shadow-md rounded text-xs"
                        style={{ top: topContextMenu.y, left: topContextMenu.x }}
                    >
                        {Object.entries(visibleColumns).map(([key, value]) => (
                            <div
                                key={key}
                                onClick={() => key !== "name" && toggleColumn(key as keyof typeof visibleColumns)}
                                className={`px-3 py-1 flex items-center gap-2 ${
                                    key !== "name" ? "hover:bg-gray-100 cursor-pointer" : "text-gray-400 cursor-not-allowed"
                                }`}
                            >
                                <input
                                    type="checkbox"
                                    readOnly
                                    checked={value}
                                    disabled={key === "name"}
                                />
                                {key.charAt(0).toUpperCase() + key.slice(1)}
                            </div>
                        ))}
                    </div>
                )}

                {/* Row Context Menu */}
                {rowContextMenu && (
                    <ContextMenu
                        x={rowContextMenu.x}
                        y={rowContextMenu.y}
                        sections={rowContextMenu.sections}
                        onClose={() => setRowContextMenu(null)}
                    />
                )}

                {filteredFiles.length === 0 && (
                    <div className="text-center text-gray-400 text-sm mt-4">No files found</div>
                )}
            </div>
        </div>
    )
}

export default MiddlePanel;