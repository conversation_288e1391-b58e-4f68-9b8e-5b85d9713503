// vite-env.d.ts
declare module '*.css';

interface ElectronAPI {
    createFolder: (name: string, parentId: number) => Promise<{ success: boolean; path?: string; error?: string }>;
    getLibraryFolders: () => Promise<FolderItem[]>;

    minimize: () => void;
    maximize: () => void;
    close: () => void;
    onMaximizeChanged?: (callback: (maximized: boolean) => void) => void;
}

interface Window {
    electron: ElectronAPI;
}
