import React from 'react';
import '../../../../style/index.css';

import {
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownSection,
    DropdownTrigger,
    User,
    Button,
    Popover,
    PopoverTrigger,
    PopoverContent,
} from "@heroui/react";

const Navbar: React.FC = () => {
    const usedStorage = 100;
    const totalStorage = 100;
    const usagePercentage = Math.round((usedStorage / totalStorage) * 100);

    return (
        <div className="w-full bg-white border-b px-3 py-1 flex justify-between items-center">
            <div className="flex items-center gap-2 text-sm font-medium">
                File/Edit/View/Tools/Help Eklenecek
            </div>

            <div className="flex items-center gap-2">
                {/* Storage */}
                <Popover placement="bottom-end">
                    <PopoverTrigger>
                        <Button
                            isIconOnly
                            radius="full"
                            size="sm"
                            className={`h-8 w-8 min-w-0 transition-all duration-300 ${
                                usagePercentage <= 25 ? 'bg-green-100 text-green-600 hover:bg-green-200' :
                                    usagePercentage <= 50 ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200' :
                                        usagePercentage <= 75 ? 'bg-orange-100 text-orange-600 hover:bg-orange-200' :
                                            'bg-red-100 text-red-600 hover:bg-red-200'
                            }`}>

                            <div className="relative">
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s8-1.79 8-4"/>
                                </svg>

                                <div className="absolute inset-0 rounded-full overflow-hidden">
                                    <div
                                        className={`absolute bottom-0 left-0 right-0 transition-all duration-500 ${
                                            usagePercentage <= 25 ? 'bg-green-500' :
                                                usagePercentage <= 50 ? 'bg-yellow-500' :
                                                    usagePercentage <= 75 ? 'bg-orange-500' :
                                                        'bg-red-500'
                                        }`}
                                        style={{ height: `${usagePercentage}%`, opacity: 0.3 }}
                                    />
                                </div>
                            </div>
                        </Button>
                    </PopoverTrigger>

                    <PopoverContent className="w-72 p-0 bg-white shadow-xl rounded-xl border border-gray-100 overflow-hidden">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3.5 border-b border-gray-100">
                            <div className="flex items-center gap-2.5">
                                <div className={`p-2 rounded-full ${
                                    usagePercentage <= 25 ? 'bg-green-100 text-green-600' :
                                        usagePercentage <= 50 ? 'bg-yellow-100 text-yellow-600' :
                                            usagePercentage <= 75 ? 'bg-orange-100 text-orange-600' :
                                                'bg-red-100 text-red-600'
                                }`}>
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s8-1.79 8-4"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-gray-900">Storage Usage</h3>
                                    <p className="text-xs text-gray-600">Current capacity status</p>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-3.5 space-y-3.5">
                            {/* Usage Stats */}
                            <div className="text-center">
                                <div className="text-xl font-bold text-gray-900">{usedStorage} GB</div>
                                <div className="text-sm text-gray-500">Used of {totalStorage} GB</div>
                            </div>

                            {/* Progress Bar */}
                            <div className="space-y-1.5">
                                <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">Usage Rate</span>
                                    <span className="font-medium text-gray-900">{usagePercentage}%</span>
                                </div>
                                <div className="w-full h-2.5 bg-gray-200 rounded-full overflow-hidden">
                                    <div
                                        className={`h-full transition-all duration-500 ${
                                            usagePercentage <= 25 ? 'bg-gradient-to-r from-green-400 to-green-500' :
                                                usagePercentage <= 50 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                                                    usagePercentage <= 75 ? 'bg-gradient-to-r from-orange-400 to-orange-500' :
                                                        'bg-gradient-to-r from-red-400 to-red-500'
                                        }`}
                                        style={{ width: `${usagePercentage}%` }}
                                    />
                                </div>
                            </div>

                            {/* Storage Breakdown */}
                            <div className="space-y-2.5">
                                <h4 className="font-medium text-gray-900">Storage Breakdown</h4>

                                <div className="space-y-1.5">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
                                            <span className="text-sm text-gray-600">Machine Learning</span>
                                        </div>

                                        <span className="text-sm font-medium">400 Mb</span>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2.5 h-2.5 bg-purple-500 rounded-full"></div>
                                            <span className="text-sm text-gray-600">Web Development</span>
                                        </div>

                                        <span className="text-sm font-medium">1 GB</span>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                                            <span className="text-sm text-gray-600">Database</span>
                                        </div>

                                        <span className="text-sm font-medium">4 GB</span>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="w-2.5 h-2.5 bg-gray-400 rounded-full"></div>
                                            <span className="text-sm text-gray-600">DevOps</span>
                                        </div>

                                        <span className="text-sm font-medium">200 Kb</span>
                                    </div>
                                </div>
                            </div>

                            {/* Warning/Info Message */}
                            <div className={`p-2.5 rounded-lg ${
                                usagePercentage <= 25 ? 'bg-green-50 text-green-800' :
                                    usagePercentage <= 50 ? 'bg-yellow-50 text-yellow-800' :
                                        usagePercentage <= 75 ? 'bg-orange-50 text-orange-800' :
                                            'bg-red-50 text-red-800'
                            }`}>
                                <div className="flex items-center gap-2">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>

                                    <span className="text-sm font-medium">
                                        {usagePercentage <= 25 ? 'Your storage status is good' :
                                            usagePercentage <= 50 ? 'Medium usage level' :
                                                usagePercentage <= 75 ? 'Storage filling up' :
                                                    'Storage almost full!'}
                                    </span>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-2">
                                <Button
                                    size="sm"
                                    className="flex-1 bg-blue-600 text-white hover:bg-blue-700"
                                >
                                    Manage Storage
                                </Button>
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
                                >
                                    Clean Up
                                </Button>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>

                <div></div>

                {/* Profile */}
                <Dropdown placement="bottom-start">
                    <DropdownTrigger>
                        <User
                            as="button"
                            avatarProps={{
                                isBordered: true,
                                radius: 'full',
                                size: 'sm',
                                className: 'h-6 w-6',
                                src: 'https://pbs.twimg.com/profile_images/482247447293337601/hBQdLi_-_400x400.png',
                            }}
                            className="transition-transform text-xs"
                            name="Eren Taha"
                            description="@erencagsak"
                        />
                    </DropdownTrigger>
                    <DropdownMenu aria-label="User menu" className="min-w-[240px] text-sm bg-gray-50 text-gray-700">
                        <DropdownSection title="Account" showDivider>
                            <DropdownItem
                                key="mail"
                                textValue="Signed <NAME_EMAIL>"
                                className="gap-1 pointer-events-none py-2"
                            >
                                <p className="text-xs font-medium text-gray-500">Signed in as</p>
                                <p className="text-sm font-medium truncate text-gray-700"><EMAIL></p>
                            </DropdownItem>

                        </DropdownSection>
                        <DropdownSection title="Profile" showDivider>
                            <DropdownItem key="profile" className="hover:bg-gray-100 rounded-md px-2 py-1 transition" description="View and edit your profile">
                                Profile Page
                            </DropdownItem>
                        </DropdownSection>
                        <DropdownSection title="Settings" showDivider>
                            <DropdownItem key="settings" className="hover:bg-gray-100 rounded-md px-2 py-1 transition" description="Customize preferences">
                                Settings
                            </DropdownItem>
                        </DropdownSection>
                        <DropdownSection showDivider>
                            <DropdownItem key="logout" color="danger" className="hover:bg-red-50 text-red-500 px-2 py-1 rounded-md transition" description="Securely sign out">
                                Log Out
                            </DropdownItem>
                        </DropdownSection>
                    </DropdownMenu>
                </Dropdown>
            </div>
        </div>
    );
};

export default Navbar;