export type ReferencesResponse = {
    id: string;
    libraryId: string;
    type: string;
    title: string;
    authors: any | null;
    editors: any | null;
    publication: string | null;
    publisher: string | null;
    year: number | null;
    volume: string | null;
    issue: string | null;
    pages: string | null;
    doi: string | null;
    isbn: string | null;
    issn: string | null;
    url: string | null;
    abstractText: string | null;
    language: string | null;
    metadata: any | null;
    tags: string[];
    notes: string | null;
    dateAdded: Date;
    dateModified: Date;
    addedBy: string;
    modifiedBy: string | null;
    citationCount: number;
    isDeleted: boolean;
    searchVector?: any | null;
}