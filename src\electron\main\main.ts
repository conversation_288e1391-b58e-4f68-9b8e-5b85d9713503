import { app, BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';

const isDev = !app.isPackaged;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mac
const isMac = process.platform === 'darwin';

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 900,
        minWidth: 800,
        minHeight: 600,
        frame: false,
        ...(isMac && { titleBarStyle: 'hiddenInset' }),
        titleBarStyle: 'hidden',
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
    });

    if (isDev) {
        mainWindow.setMenuBarVisibility(false);
        mainWindow.loadURL('http://localhost:5173')
            .catch(err => console.error('Error loading file:', err));
    } else {
        mainWindow.loadFile(path.join(__dirname, '../dist-react/index.html'))
            .catch(err => console.error('Error loading file:', err));
    }

    // Toolbar
    ipcMain.on('window-minimize', () => {
        mainWindow.minimize();
    });
    ipcMain.on('window-maximize', () => {
        if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
        } else {
            mainWindow.maximize();
        }
    });
    ipcMain.on('window-close', () => {
        app.quit();
    });
}

app.on('ready', () => {
   createWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') app.quit();
});



