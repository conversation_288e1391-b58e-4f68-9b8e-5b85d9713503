// Resize Left Panel ( Close or Open )
import React, { useEffect, useRef, useState } from "react";
import LeftPanel from "../../pages/home/<USER>/left-panel/left-panel.tsx";

const ResizableLeftPanel: React.FC = () => {
    const [panelWidth, setPanelWidth] = useState(256);
    const [isResizing, setIsResizing] = useState(false);
    const panelRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (!isResizing) return;

            const newWidth = e.clientX;
            const minWidth = 170;
            const maxWidth = 600;

            if (newWidth < minWidth) {
                setPanelWidth(0);
                setIsResizing(false);
            } else if (newWidth <= maxWidth) {
                setPanelWidth(newWidth);
            }
        };

        const handleMouseUp = () => setIsResizing(false);

        if (isResizing) {
            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
        }

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [isResizing]);

    return (
        <div
            ref={panelRef}
            style={{ width: panelWidth }}
            className="relative h-full border-r bg-white flex-shrink-0"
        >
            {panelWidth > 0 && <LeftPanel />}

            <div
                className={`absolute top-0 right-0 w-1 h-full cursor-col-resize bg-transparent hover:bg-blue-300 transition duration-200 ${isResizing ? "bg-blue-400" : ""}`}
                onMouseDown={() => setIsResizing(true)}
            />

            {panelWidth === 0 && (
                <div
                    className="absolute top-0 left-0 h-full w-4 bg-blue-100 border-r border-blue-300 flex items-center justify-center cursor-pointer hover:bg-blue-200"
                    onClick={() => setPanelWidth(256)}
                >
                    <span className="text-blue-800 text-xs">⇢</span>
                </div>
            )}
        </div>
    );
};

export default ResizableLeftPanel;
