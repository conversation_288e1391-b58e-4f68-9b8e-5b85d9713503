import React from "react";
import {<PERSON><PERSON>, <PERSON>over, <PERSON><PERSON><PERSON>ontent, PopoverTrigger} from "@heroui/react";
import {
    InformationCircleIcon,
    DocumentTextIcon,
    PaperClipIcon,
    BookOpenIcon,
    FolderIcon,
    TagIcon,
    LinkIcon,
    ArrowRightCircleIcon,
} from "@heroicons/react/24/outline";

import {useAppContext} from "../../../../components/context/use-app-context.ts";

const InfoContent = () => {
    const { selectedFile } = useAppContext();
    if (!selectedFile) return <div>Select a file first</div>;

    return (
        <div className="p-2 max-w-xs">
            <h3 className="font-semibold mb-2 flex items-center gap-1 text-blue-700">
                <InformationCircleIcon className="w-5 h-5" />
                Info
            </h3>
            <div>Title: {selectedFile.name}</div>
            <div>Creator: {selectedFile.creator}</div>
            <div>Type: {selectedFile.type}</div>
            <div>Year: {selectedFile.year}</div>
            <div>Series: {selectedFile.series}</div>
            <div>Edition: {selectedFile.edition}</div>
            <div>Language: {selectedFile.language}</div>
            <div>ISBN: {selectedFile.isbn}</div>
            <div>URL: {selectedFile.url}</div>
            <div>Pages: {selectedFile.pages}</div>
            <div>Date Added: {selectedFile.dateAdded}</div>
            <div>Modified: {selectedFile.modified}</div>
        </div>
    );
};

const AbstractContent = () => {
    const { selectedFile } = useAppContext();
    if (!selectedFile) return <div>Loading...</div>;

    return (
        <div className="p-2 max-w-xs">
            <h3 className="font-semibold mb-2 flex items-center gap-1 text-green-700">
                <DocumentTextIcon className="w-5 h-5" />
                Abstract
            </h3>
            <div>{selectedFile.abstract}</div>
        </div>
    )
}

const categoryContentMap: Record<
    string,
    React.FC
> = {
    Info: InfoContent,
    Abstract: AbstractContent,
    Attachment: () => <div className="p-2 max-w-xs">Attachment details here...</div>,
    Notes: () => <div className="p-2 max-w-xs">Notes details here...</div>,
    "Libraries and Collections": () => <div className="p-2 max-w-xs">Libraries and Collections details here...</div>,
    Tags: () => <div className="p-2 max-w-xs">Tags details here...</div>,
    Related: () => <div className="p-2 max-w-xs">Related items here...</div>,
    Locate: () => <div className="p-2 max-w-xs">Locate functionality here...</div>,
};

export const RightToolbar: React.FC = () => {
    const buttons = [
        { label: "Info", icon: InformationCircleIcon, color: "text-blue-700" },
        { label: "Abstract", icon: DocumentTextIcon, color: "text-green-700" },
        { label: "Attachment", icon: PaperClipIcon, color: "text-purple-700" },
        { label: "Notes", icon: BookOpenIcon, color: "text-yellow-700" },
        { label: "Libraries and Collections", icon: FolderIcon, color: "text-indigo-700" },
        { label: "Tags", icon: TagIcon, color: "text-pink-700" },
        { label: "Related", icon: LinkIcon, color: "text-teal-700" },
        { label: "Locate", icon: ArrowRightCircleIcon, color: "text-red-700" },
    ];

    const {selectedFile} = useAppContext();
    const isDisabled = !selectedFile;

    return (
        <div className="flex flex-col gap-3 p-1">
            {buttons.map(({ label, icon: Icon, color }) => {
                const ContentComponent = categoryContentMap[label];

                return (
                    <Popover key={label} placement="left" showArrow>
                        <PopoverTrigger asChild>
                            <Button
                                size="sm"
                                variant="ghost"
                                className={`h-8 p-0 ${color} rounded-md`}
                                disabled={isDisabled}
                                aria-label={label}
                            >
                                <Icon className="w-4 h-4" />
                            </Button>
                        </PopoverTrigger>

                        <PopoverContent className="z-50 max-w-xs bg-white rounded-md shadow-lg p-3 border border-gray-300 text-sm">
                            <ContentComponent />
                        </PopoverContent>
                    </Popover>
                );
            })}
        </div>
    );
};
