import React, { useState } from "react";
import { folderStructure } from "../../data/user-folder.ts";
import { isFolder } from "../../utils/panel-util.ts";
import { AppContext } from "./app-context.ts";
import type { FileItem, TreeItem } from "../../data/user-folder.ts";

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
    const [selectedFolderId, setSelectedFolderId] = useState<number | null>(null);
    const [folderData, setFolderData] = useState<TreeItem[]>(folderStructure);
    const [tagFilter, setTagFilter] = useState<string | null>(null);

    return (
        <AppContext.Provider
            value={{
                selectedFile,
                setSelectedFile,

                selectedFolderId,
                setSelectedFolderId,

                isFolder,

                folderData,
                setFolderData,

                tagFilter,
                setTagFilter,
            }}
        >
            {children}
        </AppContext.Provider>
    );
};
