import {useState} from "react";
import {
    Input,
    <PERSON><PERSON>,
    <PERSON>,
} from "@heroui/react";
import {getCurrentUser, signin, signup} from "../../../api/auth-api.ts";

export default function AuthPage() {
    const [tab, setTab] = useState<"login" | "register">("login");

    const [loginData, setLoginData] = useState({ email: "", password: "" });
    const [registerData, setRegisterData] = useState({
        email: "",
        password: "",
        confirmPassword: "",
    });

    const handleLogin = async () => {
        try {
            const response = await signin(loginData);

            // token'ı kaydet
            localStorage.setItem("token", response.accessToken);
            alert("Giriş başarılı!");

            console.log("Kullanıcı:", response.user);
        } catch (err: any) {
            alert(err.message);
        }
    };

    const handleRegister = async () => {
        try {
            const payload = {
                email: registerData.email,
                password: registerData.password,
                fullName: "Test Kullanıcı", // backend ne istiyorsa doldur!
                institutionId: "1",
                fieldOfStudy: "Computer Science",
                orcidId: "0000-0000-0000-0000",
                avatarUrl: "",
                preferences: {
                    language: "tr",
                    theme: "light",
                    notifications: true,
                    timezone: "Europe/Istanbul"
                },
                userType: "admin"
            };

            const response = await signup(payload);

            alert("Kayıt başarılı!");
            console.log("Yeni kullanıcı:", response);

            // opsiyonel: login tabına geç
            setTab("login");
        } catch (err: any) {
            alert(err.message);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-100 flex items-center justify-center px-4">
            <Card className="w-full max-w-md shadow-xl rounded-2xl">
                <div className="p-6 sm:p-8">
                    <div className="flex gap-4 mb-6">
                        <button
                            className={`flex-1 py-2 rounded-lg font-semibold ${
                                tab === "login"
                                    ? "bg-blue-600 text-white"
                                    : "bg-gray-100 text-gray-600"
                            }`}
                            onClick={() => setTab("login")}
                        >
                            Giriş Yap
                        </button>
                        <button
                            className={`flex-1 py-2 rounded-lg font-semibold ${
                                tab === "register"
                                    ? "bg-blue-600 text-white"
                                    : "bg-gray-100 text-gray-600"
                            }`}
                            onClick={() => setTab("register")}
                        >
                            Kayıt Ol
                        </button>
                    </div>

                    {tab === "login" && (
                        <div className="space-y-4 animate-fade-in">
                            <Input
                                type="email"
                                label="E-posta"
                                placeholder="<EMAIL>"
                                value={loginData.email}
                                onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                            />
                            <Input
                                type="password"
                                label="Şifre"
                                placeholder="••••••••"
                                value={loginData.password}
                                onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                            />
                            <Button className="w-full mt-4" onPress={handleLogin}>
                                Giriş Yap
                            </Button>
                        </div>
                    )}

                    {tab === "register" && (
                        <div className="space-y-4 animate-fade-in">
                            <Input
                                type="email"
                                label="E-posta"
                                placeholder="<EMAIL>"
                                value={registerData.email}
                                onChange={(e) => setRegisterData({ ...registerData, email: e.target.value })}
                            />
                            <Input
                                type="password"
                                label="Şifre"
                                placeholder="••••••••"
                                value={registerData.password}
                                onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
                            />
                            <Input
                                type="password"
                                label="Şifreyi Onayla"
                                placeholder="••••••••"
                                value={registerData.confirmPassword}
                                onChange={(e) =>
                                    setRegisterData({ ...registerData, confirmPassword: e.target.value })
                                }
                            />
                            <Button className="w-full mt-4" onPress={handleRegister}>
                                Kayıt Ol
                            </Button>
                        </div>
                    )}
                </div>
            </Card>
            <Button
                onClick={async () => {
                    const token = localStorage.getItem("token");
                    if (!token) return alert("Token yok. Lütfen giriş yap.");

                    try {
                        const user = await getCurrentUser(token);
                        alert(`Giriş yapan: ${user.fullName}`);
                    } catch (err: any) {
                        alert(err.message);
                    }
                }}
            >
                Mevcut Kullanıcıyı Getir
            </Button>
        </div>
    );
}
