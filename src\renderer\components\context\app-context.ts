import React, { createContext } from "react";
import type { FileItem, FolderItem, TreeItem } from "../../data/user-folder.ts";

export interface AppContextType {
    selectedFile: FileItem | null;
    setSelectedFile: React.Dispatch<React.SetStateAction<FileItem | null>>;

    selectedFolderId: number | null;
    setSelectedFolderId: React.Dispatch<React.SetStateAction<number | null>>;

    isFolder: (item: TreeItem) => item is FolderItem;

    folderData: TreeItem[];
    setFolderData: React.Dispatch<React.SetStateAction<TreeItem[]>>;

    tagFilter: string | null;
    setTagFilter: React.Dispatch<React.SetStateAction<string | null>>;
}

export const AppContext = createContext<AppContextType | undefined>(undefined);
